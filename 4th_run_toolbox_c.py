import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
import pandas as pd
from scipy.optimize import curve_fit
from sympy import sympify, symbols, lambdify
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import matplotlib.cm as cm
import matplotlib.pyplot as plt  # Add this import for the Normalize class

class CurveFittingTool(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Curve Fitting Tool")
        self.geometry("900x700")  # Increased size to accommodate new settings

        # Initial sample data
        x = np.linspace(0, 50, 100)
        y = 2 * x + 3 + np.random.normal(0, 5, 100)
        z = np.random.choice(['A', 'B', 'C'], 100)  # Sample Z (color key) data
        index = np.arange(100)  # Sample index data
        self.data = {'x': x, 'y': y, 'z': z, 'index': index}

        # Define pre-defined custom functions
        self.custom_functions = {
            "Logistic": {
                "model": lambda x, L, k, x0: L / (1 + np.exp(-k * (x - x0))),
                "equation": "f(x) = L / (1 + e^(-k(x - x0)))",
                "params": ["L", "k", "x0"]
            },
            "Gaussian": {
                "model": lambda x, a, b, c: a * np.exp(-((x - b) ** 2) / (2 * c ** 2)),
                "equation": "f(x) = a * e^(-(x - b)^2 / (2c^2))",
                "params": ["a", "b", "c"]
            },
            "Sinusoidal": {
                "model": lambda x, a, b, c: a * np.sin(b * x + c),
                "equation": "f(x) = a * sin(bx + c)",
                "params": ["a", "b", "c"]
            }
        }

        # Initial plot settings
        self.x_min = tk.DoubleVar(value=-1500)
        self.x_max = tk.DoubleVar(value=-1200)
        self.y_min = tk.DoubleVar(value=-1500)
        self.y_max = tk.DoubleVar(value=-1200)
        self.point_size = tk.DoubleVar(value=50)
        self.colormap = tk.StringVar(value='viridis')
        # Replace custom_tick_labels with tick_min, tick_max, tick_step as in 4th_run_toolbox_c.py
        self.tick_min = tk.DoubleVar(value=0)
        self.tick_max = tk.DoubleVar(value=1)
        self.tick_step = tk.DoubleVar(value=0.2)

        # --- Control Panel (Left) ---
        control_frame = tk.Frame(self)
        control_frame.pack(side=tk.LEFT, padx=10, pady=10, fill=tk.Y)

        # Select Data File Button
        self.select_file_button = tk.Button(control_frame, text="Select Data File", command=self.import_data)
        self.select_file_button.pack(pady=5)

        # Fit name
        tk.Label(control_frame, text="Fit name:").pack()
        self.fit_name_entry = tk.Entry(control_frame)
        self.fit_name_entry.pack()
        self.fit_name_entry.insert(0, "untitled fit 1")

        # Fit type
        tk.Label(control_frame, text="Fit type:").pack()
        self.fit_type_var = tk.StringVar(value="Linear")
        fit_types = ["Linear", "Power", "Exponential", "Polynomial", "Custom", "Pre-defined Custom"]
        self.fit_type_dropdown = ttk.Combobox(control_frame, textvariable=self.fit_type_var, values=fit_types)
        self.fit_type_dropdown.pack()
        self.fit_type_dropdown.bind("<<ComboboxSelected>>", self.on_fit_type_change)

        # Custom function entry (initially hidden)
        self.custom_func_label = tk.Label(control_frame, text="Custom function (e.g., a*x + b):")
        self.custom_func_entry = tk.Entry(control_frame)
        self.custom_func_entry.insert(0, "a*x + b")

        # Pre-defined custom function dropdown (initially hidden)
        self.predefined_func_label = tk.Label(control_frame, text="Pre-defined function:")
        self.predefined_func_var = tk.StringVar(value="Logistic")
        self.predefined_func_dropdown = ttk.Combobox(control_frame, textvariable=self.predefined_func_var, values=list(self.custom_functions.keys()))
        self.predefined_func_dropdown.pack()

        # Degree (for polynomial)
        self.degree_label = tk.Label(control_frame, text="Degree:")
        self.degree_var = tk.StringVar(value="3")
        degrees = [str(i) for i in range(1, 6)]
        self.degree_dropdown = ttk.Combobox(control_frame, textvariable=self.degree_var, values=degrees)
        self.degree_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Auto fit checkbox
        self.auto_fit_var = tk.BooleanVar(value=False)
        self.auto_fit_check = tk.Checkbutton(control_frame, text="Auto fit", variable=self.auto_fit_var)
        self.auto_fit_check.pack()

        # X data
        tk.Label(control_frame, text="X data:").pack()
        self.x_data_var = tk.StringVar(value="x")
        self.x_data_dropdown = ttk.Combobox(control_frame, textvariable=self.x_data_var)
        self.x_data_dropdown['values'] = list(self.data.keys())
        self.x_data_dropdown.pack()
        self.x_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Y data
        tk.Label(control_frame, text="Y data:").pack()
        self.y_data_var = tk.StringVar(value="y")
        self.y_data_dropdown = ttk.Combobox(control_frame, textvariable=self.y_data_var)
        self.y_data_dropdown['values'] = list(self.data.keys())
        self.y_data_dropdown.pack()
        self.y_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Z data (color key)
        tk.Label(control_frame, text="Z data (color key):").pack()
        self.z_data_var = tk.StringVar(value="z")
        self.z_data_dropdown = ttk.Combobox(control_frame, textvariable=self.z_data_var)
        self.z_data_dropdown['values'] = list(self.data.keys())
        self.z_data_dropdown.pack()
        self.z_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Index data
        tk.Label(control_frame, text="Index data:").pack()
        self.index_data_var = tk.StringVar(value="index")
        self.index_data_dropdown = ttk.Combobox(control_frame, textvariable=self.index_data_var)
        self.index_data_dropdown['values'] = list(self.data.keys())
        self.index_data_dropdown.pack()
        self.index_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Fit and Stop buttons
        self.fit_button = tk.Button(control_frame, text="Fit", command=self.perform_fit)
        self.fit_button.pack(pady=5)
        self.stop_button = tk.Button(control_frame, text="Stop", state="disabled")
        self.stop_button.pack()

        # Results frame
        results_frame = tk.LabelFrame(control_frame, text="Results")
        results_frame.pack(pady=10, fill=tk.X)
        self.equation_label = tk.Label(results_frame, text="", wraplength=200)
        self.equation_label.pack()
        self.param_labels = []
        for i in range(5):  # Up to 5 parameters
            label = tk.Label(results_frame, text="")
            label.pack()
            self.param_labels.append(label)
        self.sse_label = tk.Label(results_frame, text="SSE: ")
        self.sse_label.pack()
        self.rsquare_label = tk.Label(results_frame, text="R-square: ")
        self.rsquare_label.pack()
        self.rmse_label = tk.Label(results_frame, text="RMSE: ")
        self.rmse_label.pack()

        # --- Plot Settings Frame ---
        settings_frame = tk.LabelFrame(control_frame, text="Plot Settings")
        settings_frame.pack(pady=10, fill=tk.X)

        # X-axis limits
        tk.Label(settings_frame, text="X Min:").grid(row=0, column=0, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.x_min).grid(row=0, column=1, padx=5, pady=2)
        tk.Label(settings_frame, text="X Max:").grid(row=0, column=2, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.x_max).grid(row=0, column=3, padx=5, pady=2)

        # Y-axis limits
        tk.Label(settings_frame, text="Y Min:").grid(row=1, column=0, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.y_min).grid(row=1, column=1, padx=5, pady=2)
        tk.Label(settings_frame, text="Y Max:").grid(row=1, column=2, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.y_max).grid(row=1, column=3, padx=5, pady=2)

        # Point size
        tk.Label(settings_frame, text="Point Size:").grid(row=2, column=0, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.point_size).grid(row=2, column=1, padx=5, pady=2)

        # Colormap selection
        tk.Label(settings_frame, text="Colormap:").grid(row=3, column=0, padx=5, pady=2)
        colormap_options = ['viridis', 'plasma', 'inferno', 'magma', 'cividis']
        ttk.Combobox(settings_frame, textvariable=self.colormap, values=colormap_options).grid(row=3, column=1, padx=5, pady=2)

        # Tick parameters (replacing custom tick labels)
        tk.Label(settings_frame, text="Tick Min:").grid(row=4, column=0, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.tick_min).grid(row=4, column=1, padx=5, pady=2)
        tk.Label(settings_frame, text="Tick Max:").grid(row=4, column=2, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.tick_max).grid(row=4, column=3, padx=5, pady=2)
        tk.Label(settings_frame, text="Tick Step:").grid(row=5, column=0, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.tick_step).grid(row=5, column=1, padx=5, pady=2)

        # Apply Settings button
        tk.Button(settings_frame, text="Apply Settings", command=self.apply_plot_settings).grid(row=6, column=0, columnspan=4, pady=5)

        # --- Plot Frame (Right) ---
        plot_frame = tk.Frame(self)
        plot_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        # Create figure with fixed size
        self.figure = Figure(figsize=(5, 4), dpi=100)
        self.ax = self.figure.add_subplot(111)
        # Disable automatic layout adjustments
        self.figure.set_constrained_layout(False)
        self.canvas = FigureCanvasTkAgg(self.figure, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Set initial fixed axis limits based on your data range
        self.initial_x_min, self.initial_x_max = -1500, -1200
        self.initial_y_min, self.initial_y_max = -1500, -1200
        self.ax.set_xlim(self.initial_x_min, self.initial_x_max)
        self.ax.set_ylim(self.initial_y_min, self.initial_y_max)
        self.ax.set_autoscale_on(False)  # Disable autoscaling

        # Initial setup
        self.on_fit_type_change()  # Set initial visibility
        self.perform_fit()

    def parse_range(self, range_str, max_value, label):
        """Parse a range string (e.g., '1-3') and validate it."""
        if not range_str:
            return slice(None)  # Default to all if empty
        try:
            if '-' in range_str:
                start, end = map(int, range_str.split('-'))
                if start < 1 or end > max_value or start > end:
                    raise ValueError(f"Invalid {label} range. Must be between 1 and {max_value} and start <= end.")
                return slice(start - 1, end)  # Convert to 0-based indexing
            else:
                idx = int(range_str) - 1
                if idx < 0 or idx >= max_value:
                    raise ValueError(f"Invalid {label} index. Must be between 1 and {max_value}.")
                return slice(idx, idx + 1)
        except ValueError as e:
            messagebox.showerror("Error", str(e))
            return None

    def find_first_numeric_column(self, df):
        """Find the first column with at least some numeric data."""
        for i, col in enumerate(df.columns):
            try:
                if pd.to_numeric(df[col], errors='coerce').notna().any():
                    return i
            except Exception:
                continue
        return 0  # Default to first column if no numeric data is found

    def import_data(self):
        """Import data from Excel or CSV and allow range selection with preview and scrollable column selection"""
        file_path = filedialog.askopenfilename(
            title="Select Excel or CSV file",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("CSV files", "*.csv")]
        )
        if not file_path:
            return

        try:
            # Load the file
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
                print(f"CSV loaded: {df.shape} rows, columns: {list(df.columns)}")
            else:
                # Excel file: prompt for sheet selection
                excel_file = pd.ExcelFile(file_path)
                sheet_names = excel_file.sheet_names
                if not sheet_names:
                    messagebox.showerror("Error", "No sheets found in the Excel file.")
                    return
                sheet_name = tk.StringVar(value=sheet_names[0])
                sheet_dialog = tk.Toplevel(self)
                sheet_dialog.title("Select Sheet")
                tk.Label(sheet_dialog, text="Select sheet:").pack()
                sheet_dropdown = ttk.Combobox(sheet_dialog, textvariable=sheet_name, values=sheet_names)
                sheet_dropdown.pack()
                tk.Button(sheet_dialog, text="OK", command=sheet_dialog.destroy).pack()
                sheet_dialog.wait_window()
                selected_sheet = sheet_name.get()
                df = pd.read_excel(file_path, sheet_name=selected_sheet)
                print(f"Excel sheet '{selected_sheet}' loaded: {df.shape} rows, columns: {list(df.columns)}")

            # Create range selection dialog
            range_dialog = tk.Toplevel(self)
            range_dialog.title("Select Data Ranges")
            range_dialog.geometry("800x500")  # Increased size to accommodate more columns

            # Preview Table (all rows for scrolling)
            preview_frame = ttk.Frame(range_dialog)
            preview_frame.pack(pady=10, fill=tk.BOTH, expand=True)

            # Add row numbers to the DataFrame for display
            preview_df = df.copy()
            preview_df.insert(0, "Row", range(1, len(df) + 1))  # 1-based indexing for row numbers

            preview_tree = ttk.Treeview(preview_frame, show="headings")
            preview_tree["columns"] = list(preview_df.columns)
            for col in preview_df.columns:
                if col == "Row":
                    preview_tree.heading(col, text=col)
                    preview_tree.column(col, width=50, minwidth=50)
                else:
                    col_idx = df.columns.get_loc(col) + 1  # Adjust for 0-based index, +1 for 1-based display
                    preview_tree.heading(col, text=f"{col_idx}: {col}")
                    preview_tree.column(col, width=120, minwidth=100)  # Increased width for better visibility
            for idx, row in preview_df.iterrows():
                preview_tree.insert("", tk.END, values=list(row))

            # Add vertical scrollbar
            v_scroll = ttk.Scrollbar(preview_frame, orient="vertical", command=preview_tree.yview)
            v_scroll.pack(side=tk.RIGHT, fill=tk.Y)
            preview_tree.configure(yscrollcommand=v_scroll.set)

            # Add horizontal scrollbar
            h_scroll = ttk.Scrollbar(preview_frame, orient="horizontal", command=preview_tree.xview)
            h_scroll.pack(side=tk.BOTTOM, fill=tk.X)
            preview_tree.configure(xscrollcommand=h_scroll.set)

            preview_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            # Row range input
            tk.Label(range_dialog, text="Row Range (e.g., 1-10 or leave blank for all):").pack()
            row_range_entry = tk.Entry(range_dialog)
            row_range_entry.pack()

            # X column selection with scrollable combobox
            tk.Label(range_dialog, text="X Column:").pack()
            tk.Label(range_dialog, text="Note: Column A = 1, B = 2, etc.").pack()
            x_col_var = tk.StringVar()
            x_col_options = [f"{i}: {col}" for i, col in enumerate(df.columns, 1)]
            x_col_combobox = ttk.Combobox(range_dialog, textvariable=x_col_var, values=x_col_options)
            x_col_combobox.pack()
            # Default to the first column with numeric data
            first_numeric_idx = self.find_first_numeric_column(df)
            x_col_combobox.set(x_col_options[first_numeric_idx])

            # Y column selection with scrollable combobox
            tk.Label(range_dialog, text="Y Column:").pack()
            tk.Label(range_dialog, text="Note: Column A = 1, B = 2, etc.").pack()
            y_col_var = tk.StringVar()
            y_col_options = [f"{i}: {col}" for i, col in enumerate(df.columns, 1)]
            y_col_combobox = ttk.Combobox(range_dialog, textvariable=y_col_var, values=y_col_options)
            y_col_combobox.pack()
            # Default to the next column with numeric data, if available
            second_numeric_idx = min(first_numeric_idx + 1, len(df.columns) - 1)
            if not pd.to_numeric(df.iloc[:, second_numeric_idx], errors='coerce').notna().any():
                second_numeric_idx = first_numeric_idx  # Fallback to the first numeric column
            y_col_combobox.set(y_col_options[second_numeric_idx])

            # Z column selection with scrollable combobox (color key)
            tk.Label(range_dialog, text="Z Column (color key, optional):").pack()
            tk.Label(range_dialog, text="Select 'None' to use a single color.").pack()
            z_col_var = tk.StringVar()
            z_col_options = ["None"] + [f"{i}: {col}" for i, col in enumerate(df.columns, 1)]
            z_col_combobox = ttk.Combobox(range_dialog, textvariable=z_col_var, values=z_col_options)
            z_col_combobox.pack()
            z_col_combobox.set("None")  # Default to None (single color)

            # Index column selection with combobox
            tk.Label(range_dialog, text="Index Column (optional for point labels):").pack()
            tk.Label(range_dialog, text="Select 'None' to disable point labels.").pack()
            index_col_var = tk.StringVar()
            index_col_options = ["None"] + [f"{i}: {col}" for i, col in enumerate(df.columns, 1)]
            index_col_combobox = ttk.Combobox(range_dialog, textvariable=index_col_var, values=index_col_options)
            index_col_combobox.pack()
            index_col_combobox.set("None")  # Default to None

            def apply_ranges():
                row_range_str = row_range_entry.get()
                x_col_input = x_col_var.get().split(":")[0].strip()  # Extract index (e.g., "1" from "1: Time")
                y_col_input = y_col_var.get().split(":")[0].strip()  # Extract index (e.g., "2" from "2: Value1")
                z_col_input = z_col_var.get()  # Could be "None" or "3: Category"
                index_col_input = index_col_var.get()  # Get selected value from combobox

                # Parse row range
                row_slice = self.parse_range(row_range_str, df.shape[0], "row")
                if row_slice is None:
                    return

                # Parse X column index
                try:
                    x_col_idx = int(x_col_input) - 1  # Convert to 0-based
                    if x_col_idx < 0 or x_col_idx >= df.shape[1]:
                        raise ValueError(f"X column index out of range. Must be between 1 and {df.shape[1]}.")
                except ValueError as e:
                    messagebox.showerror("Error", str(e))
                    return

                # Parse Y column index
                try:
                    y_col_idx = int(y_col_input) - 1  # Convert to 0-based
                    if y_col_idx < 0 or y_col_idx >= df.shape[1]:
                        raise ValueError(f"Y column index out of range. Must be between 1 and {df.shape[1]}.")
                except ValueError as e:
                    messagebox.showerror("Error", str(e))
                    return

                # Parse Z column index (if not "None")
                z_col_idx = None
                if z_col_input != "None":
                    try:
                        z_col_idx = int(z_col_input.split(":")[0].strip()) - 1  # Convert to 0-based
                        if z_col_idx < 0 or z_col_idx >= df.shape[1]:
                            raise ValueError(f"Z column index out of range. Must be between 1 and {df.shape[1]}.")
                    except ValueError as e:
                        messagebox.showerror("Error", str(e))
                        return

                # Parse Index column (if not "None")
                index_col_idx = None
                index_col_name = None
                if index_col_input != "None":
                    try:
                        index_col_idx = int(index_col_input.split(":")[0].strip()) - 1  # Convert to 0-based
                        if index_col_idx < 0 or index_col_idx >= df.shape[1]:
                            raise ValueError(f"Index column index out of range. Must be between 1 and {df.shape[1]}.")
                        index_col_name = df.columns[index_col_idx]
                    except ValueError as e:
                        messagebox.showerror("Error", str(e))
                        return

                # Extract columns to include
                columns_to_extract = [x_col_idx, y_col_idx]
                if z_col_idx is not None:
                    columns_to_extract.append(z_col_idx)
                if index_col_idx is not None:
                    columns_to_extract.append(index_col_idx)

                # Extract subset DataFrame
                subset_df = df.iloc[row_slice, columns_to_extract]
                subset_df = subset_df.dropna()

                # Attempt to convert X and Y data to numeric
                try:
                    x_data = pd.to_numeric(subset_df.iloc[:, 0], errors='coerce').values
                    y_data = pd.to_numeric(subset_df.iloc[:, 1], errors='coerce').values
                except Exception as e:
                    messagebox.showerror("Error", f"Failed to convert X or Y data to numeric: {e}")
                    return

                # Check if X and Y data contain any valid values
                if not np.any(~np.isnan(x_data)) or not np.any(~np.isnan(y_data)):
                    messagebox.showerror("Error", "Selected X or Y column contains only NaN values. Please select columns with numeric data.")
                    return

                # Handle Z data (if provided)
                z_data = None
                if z_col_idx is not None:
                    z_data = subset_df.iloc[:, 2 if index_col_idx is None else 3].values
                    # Attempt to convert Z data to numeric, otherwise treat as categorical
                    try:
                        z_data_numeric = pd.to_numeric(z_data, errors='coerce')
                        if np.all(np.isnan(z_data_numeric)):
                            # If all NaN, treat as categorical
                            z_data = z_data.astype(str)
                        else:
                            z_data = z_data_numeric
                    except Exception:
                        z_data = z_data.astype(str)

                # Handle Index data (if provided)
                index_data = None
                if index_col_idx is not None:
                    index_data = subset_df.iloc[:, 2 if z_col_idx is None else 3].values

                # Check for NaN values in X and Y after conversion
                if np.any(np.isnan(x_data)) or np.any(np.isnan(y_data)):
                    messagebox.showwarning("Warning", "Some values in X or Y columns could not be converted to numeric and will be ignored.")
                    # Filter out rows where either X or Y is NaN
                    valid_mask = ~np.isnan(x_data) & ~np.isnan(y_data)
                    x_data = x_data[valid_mask]
                    y_data = y_data[valid_mask]
                    if z_data is not None:
                        z_data = z_data[valid_mask]
                    if index_data is not None:
                        index_data = index_data[valid_mask]

                # Check if any data remains after filtering
                if len(x_data) == 0 or len(y_data) == 0:
                    messagebox.showerror("Error", "No valid numeric data remaining after conversion and filtering.")
                    return

                # Update data dictionary with column names as keys
                x_col_name = df.columns[x_col_idx]
                y_col_name = df.columns[y_col_idx]
                z_col_name = df.columns[z_col_idx] if z_col_idx is not None else None
                index_col_name = df.columns[index_col_idx] if index_col_idx is not None else None

                self.data.clear()
                self.data[x_col_name] = x_data
                self.data[y_col_name] = y_data
                if z_col_name:
                    self.data[z_col_name] = z_data
                if index_col_name:
                    self.data[index_col_name] = index_data

                # Update dropdowns with the actual column names
                dropdown_keys = [x_col_name, y_col_name]
                if z_col_name:
                    dropdown_keys.append(z_col_name)
                if index_col_name:
                    dropdown_keys.append(index_col_name)

                self.x_data_dropdown['values'] = dropdown_keys
                self.y_data_dropdown['values'] = dropdown_keys
                self.z_data_dropdown['values'] = dropdown_keys
                self.index_data_dropdown['values'] = dropdown_keys
                self.x_data_var.set(x_col_name)
                self.y_data_var.set(y_col_name)
                self.z_data_var.set(z_col_name if z_col_name else "")
                self.index_data_var.set(index_col_name if index_col_name else "")
                self.perform_fit()
                range_dialog.destroy()

            tk.Button(range_dialog, text="Apply", command=apply_ranges).pack(pady=10)
            range_dialog.transient(self)
            range_dialog.grab_set()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load file: {e}")
            print(f"Error details: {str(e)}")

    def on_fit_type_change(self, *args):
        """Show/hide custom function entry and degree dropdown based on fit type"""
        fit_type = self.fit_type_var.get()
        if fit_type == "Custom":
            self.custom_func_label.pack()
            self.custom_func_entry.pack()
            self.predefined_func_label.pack_forget()
            self.predefined_func_dropdown.pack_forget()
            self.degree_label.pack_forget()
            self.degree_dropdown.pack_forget()
        elif fit_type == "Polynomial":
            self.custom_func_label.pack_forget()
            self.custom_func_entry.pack_forget()
            self.predefined_func_label.pack_forget()
            self.predefined_func_dropdown.pack_forget()
            self.degree_label.pack()
            self.degree_dropdown.pack()
        elif fit_type == "Pre-defined Custom":
            self.custom_func_label.pack_forget()
            self.custom_func_entry.pack_forget()
            self.predefined_func_label.pack()
            self.predefined_func_dropdown.pack()
            self.degree_label.pack_forget()
            self.degree_dropdown.pack_forget()
        else:
            self.custom_func_label.pack_forget()
            self.custom_func_entry.pack_forget()
            self.predefined_func_label.pack_forget()
            self.predefined_func_dropdown.pack_forget()
            self.degree_label.pack_forget()
            self.degree_dropdown.pack_forget()
        if self.auto_fit_var.get():
            self.perform_fit()

    def on_param_change(self, *args):
        """Trigger fit if auto fit is enabled"""
        if self.auto_fit_var.get():
            self.perform_fit()

    def apply_plot_settings(self):
        """Apply the updated plot settings and redraw the plot"""
        # Validate input values
        try:
            x_min = float(self.x_min.get())
            x_max = float(self.x_max.get())
            y_min = float(self.y_min.get())
            y_max = float(self.y_max.get())
            point_size = float(self.point_size.get())
            tick_min = float(self.tick_min.get())
            tick_max = float(self.tick_max.get())
            tick_step = float(self.tick_step.get())

            if x_min >= x_max:
                raise ValueError("X Min must be less than X Max.")
            if y_min >= y_max:
                raise ValueError("Y Min must be less than Y Max.")
            if point_size <= 0:
                raise ValueError("Point size must be positive.")
            if tick_min >= tick_max:
                raise ValueError("Tick Min must be less than Tick Max.")
            if tick_step <= 0:
                raise ValueError("Tick Step must be positive.")

            # Update plot settings
            self.ax.set_xlim(x_min, x_max)
            self.ax.set_ylim(y_min, y_max)
            self.canvas.draw()
            self.perform_fit()  # Redraw with updated settings
        except ValueError as e:
            messagebox.showerror("Error", f"Invalid plot settings: {e}")

    def perform_fit(self):
        """Perform fitting based on selected type and update plot and results"""
        try:
            x_key = self.x_data_var.get()
            y_key = self.y_data_var.get()
            z_key = self.z_data_var.get()
            index_key = self.index_data_var.get()

            if x_key not in self.data or y_key not in self.data:
                messagebox.showerror("Error", "Selected X or Y data not found in the dataset. Please select valid columns.")
                return

            x = self.data[x_key]
            y = self.data[y_key]
            z = self.data[z_key] if z_key in self.data else None
            index = self.data[index_key] if index_key in self.data else None

            # Ensure there's at least some valid data to plot
            if not np.any(~np.isnan(x)) or not np.any(~np.isnan(y)):
                messagebox.showerror("Error", f"No valid numeric data in X ('{x_key}') or Y ('{y_key}') columns. Please select columns with numeric data.")
                return

            min_len = min(len(x), len(y))
            x = x[:min_len]
            y = y[:min_len]
            if z is not None:
                z = z[:min_len]
            if index is not None:
                index = index[:min_len]

            fit_type = self.fit_type_var.get()
            if fit_type == "Polynomial":
                degree = int(self.degree_var.get())
                coeffs = np.polyfit(x, y, degree)
                poly = np.poly1d(coeffs)
                fitted_y = poly(x)
                equation = f"Polynomial (degree {degree}): f(x) = " + " + ".join(
                    [f"{c:.2f}x^{degree-i}" if i < degree else f"{c:.2f}" for i, c in enumerate(coeffs)]
                )
                params = [f"p{i+1} = {c:.2f}" for i, c in enumerate(coeffs)]
            elif fit_type == "Custom":
                func_str = self.custom_func_entry.get()
                try:
                    params = symbols('a b c d e')
                    x_sym = symbols('x')
                    expr = sympify(func_str)
                    model = lambdify((x_sym, *params), expr, "numpy")
                    param_names = [str(p) for p in params if p in expr.free_symbols]
                    num_params = len(param_names)
                    p0 = [1.0] * num_params
                    popt, _ = curve_fit(model, x, y, p0=p0, maxfev=10000)
                    fitted_y = model(x, *popt)
                    equation = f"Custom: f(x) = {func_str}"
                    params = [f"{name} = {val:.2f}" for name, val in zip(param_names, popt)]
                except Exception as e:
                    messagebox.showerror("Error", f"Invalid custom function: {e}")
                    return
            elif fit_type == "Pre-defined Custom":
                func_name = self.predefined_func_var.get()
                if func_name not in self.custom_functions:
                    messagebox.showerror("Error", f"Unknown pre-defined function: {func_name}")
                    return
                model = self.custom_functions[func_name]["model"]
                equation = self.custom_functions[func_name]["equation"]
                param_names = self.custom_functions[func_name]["params"]
                num_params = len(param_names)
                p0 = [1.0] * num_params  # Initial guess for parameters
                popt, _ = curve_fit(model, x, y, p0=p0, maxfev=10000)
                fitted_y = model(x, *popt)
                params = [f"{name} = {val:.2f}" for name, val in zip(param_names, popt)]
            else:
                if fit_type == "Linear":
                    def model(x, a, b):
                        return a * x + b
                    equation_template = "Linear: f(x) = a x + b"
                    param_names = ["a", "b"]
                elif fit_type == "Power":
                    def model(x, a, b):
                        return a * x**b
                    equation_template = "Power: f(x) = a x^b"
                    param_names = ["a", "b"]
                elif fit_type == "Exponential":
                    def model(x, a, b):
                        return a * np.exp(b * x)
                    equation_template = "Exponential: f(x) = a e^(b x)"
                    param_names = ["a", "b"]

                popt, _ = curve_fit(model, x, y, maxfev=10000)
                fitted_y = model(x, *popt)
                equation = equation_template.replace("a", f"{popt[0]:.2f}").replace("b", f"{popt[1]:.2f}")
                params = [f"{name} = {val:.2f}" for name, val in zip(param_names, popt)]

            self.equation_label.config(text=equation)
            for i, param in enumerate(params):
                self.param_labels[i].config(text=param)
            for i in range(len(params), 5):
                self.param_labels[i].config(text="")

            residuals = y - fitted_y
            sse = np.sum(residuals**2)
            sst = np.sum((y - np.mean(y))**2)
            rsquare = 1 - (sse / sst) if sst != 0 else 0
            rmse = np.sqrt(sse / len(y))
            self.sse_label.config(text=f"SSE: {sse:.2f}")
            self.rsquare_label.config(text=f"R-square: {rsquare:.4f}")
            self.rmse_label.config(text=f"RMSE: {rmse:.2f}")

            self.ax.clear()
            # Remove any existing colorbars
            for cbar in self.figure.axes:
                if cbar != self.ax:
                    self.figure.delaxes(cbar)

            # Scatter plot with color based on Z (if provided)
            if z is not None:
                try:
                    # Attempt to convert Z to numeric, coerce non-numeric to NaN
                    z_numeric = pd.to_numeric(z, errors='coerce')
                    # Check if Z is fully numeric after conversion
                    if np.all(np.isnan(z_numeric)):
                        # If all values are NaN, treat as categorical
                        z_unique = np.unique(z)
                        z_map = {val: i for i, val in enumerate(z_unique)}  # Map to 0, 1, 2, ...
                        z_colors = np.array([z_map.get(val, 0) for val in z])
                        norm = plt.Normalize(0, len(z_unique)-1)  # Normalize to match colormap range
                        cmap = plt.get_cmap(self.colormap.get(), len(z_unique))
                        scatter = self.ax.scatter(x, y, c=z_colors, cmap=cmap, norm=norm, s=self.point_size.get(), label='Data')
                        cbar = self.figure.colorbar(scatter, ax=self.ax, fraction=0.046, pad=0.04)
                        cbar.set_label(z_key)
                        # Set ticks at the middle of each color segment
                        tick_positions = np.arange(0, len(z_unique))
                        cbar.set_ticks(tick_positions)
                        # Use numerical values (0, 1, 2...) as tick labels instead of category values
                        cbar.set_ticklabels([f"{i:.2f}" for i in tick_positions])
                    else:
                        # Numeric Z data (including mixed with NaN)
                        valid_mask = ~np.isnan(z_numeric)
                        z_colors = np.zeros_like(z_numeric, dtype=float)
                        z_colors[valid_mask] = z_numeric[valid_mask]
                        cmap = cm.get_cmap(self.colormap.get())
                        scatter = self.ax.scatter(x, y, c=z_colors, cmap=cmap, s=self.point_size.get(), label='Data')
                        cbar = self.figure.colorbar(scatter, ax=self.ax, fraction=0.046, pad=0.04)
                        cbar.set_label(z_key)
                        # Apply user-defined tick settings for numeric data
                        try:
                            tick_min = self.tick_min.get()
                            tick_max = self.tick_max.get()
                            tick_step = self.tick_step.get()
                            if tick_step > 0 and tick_min < tick_max:
                                ticks = np.arange(tick_min, tick_max + tick_step/2, tick_step)  # Added tick_step/2 to include max
                                # Only use ticks that are within the actual z_colors range
                                z_min, z_max = np.min(z_colors[valid_mask]), np.max(z_colors[valid_mask])
                                valid_ticks = ticks[(ticks >= z_min) & (ticks <= z_max)]
                                if len(valid_ticks) > 0:
                                    cbar.set_ticks(valid_ticks)
                                    cbar.set_ticklabels([f"{t:.2f}" for t in valid_ticks])
                        except ValueError as e:
                            messagebox.showwarning("Warning", f"Invalid tick settings: {e}. Using default ticks.")
                except Exception as e:
                    print(f"Error in Z data handling: {e}")
                    # Fallback: Treat Z as categorical
                    z_unique = np.unique(z)
                    z_map = {val: i for i, val in enumerate(z_unique)}  # Map to 0, 1, 2, ... for colormap indexing
                    z_colors = np.array([z_map.get(val, 0) for val in z])
                    norm = plt.Normalize(0, len(z_unique)-1)  # Normalize to match colormap range
                    cmap = plt.get_cmap(self.colormap.get(), len(z_unique))
                    scatter = self.ax.scatter(x, y, c=z_colors, cmap=cmap, norm=norm, s=self.point_size.get(), label='Data')
                    cbar = self.figure.colorbar(scatter, ax=self.ax, fraction=0.046, pad=0.04)
                    cbar.set_label(z_key)
                    # Set ticks at the middle of each color segment
                    tick_positions = np.arange(0, len(z_unique))
                    cbar.set_ticks(tick_positions)
                    cbar.set_ticklabels([str(val) for val in z_unique])  # Use actual category values
            else:
                self.ax.scatter(x, y, color='black', s=self.point_size.get(), label='Data')

            if index is not None:
                for i, txt in enumerate(index):
                    self.ax.annotate(str(txt), (x[i], y[i]), fontsize=8,
                                     xytext=(5, 5), textcoords='offset points')

            # Generate x_plot within custom limits
            x_plot = np.linspace(float(self.x_min.get()), float(self.x_max.get()), 100)
            if fit_type == "Polynomial":
                y_plot = poly(x_plot)
            elif fit_type == "Custom" or fit_type == "Pre-defined Custom":
                y_plot = model(x_plot, *popt)
            else:
                y_plot = model(x_plot, *popt)
            self.ax.plot(x_plot, y_plot, 'b-', label='Fit')
            self.ax.grid(True)
            self.ax.set_xlim(float(self.x_min.get()), float(self.x_max.get()))
            self.ax.set_ylim(float(self.y_min.get()), float(self.y_max.get()))
            self.ax.legend()
            self.canvas.draw()
        except Exception as e:
            messagebox.showerror("Error", f"Fitting failed: {e}")

if __name__ == "__main__":
    app = CurveFittingTool()
    app.mainloop()
