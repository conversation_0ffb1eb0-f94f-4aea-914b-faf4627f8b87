import numpy as np

# Simple test to verify polynomial fitting works
print("Testing polynomial fitting...")

# Generate test data
x = np.array([1, 2, 3, 4, 5])
y = np.array([2, 8, 18, 32, 50])  # y = 2*x^2

# Fit polynomial
coeffs = np.polyfit(x, y, 2)
print(f"Coefficients: {coeffs}")
print(f"Expected: [2, 0, 0] approximately")

# Test if the method exists in our class
try:
    import sys
    sys.path.append('.')
    
    # Import the class to check if methods exist
    from importlib import import_module
    spec = import_module('6th_run_toolbox_PyQT_final')
    
    # Check if the methods exist
    cls = spec.CurveFittingTool
    methods = ['fit_polynomial', 'fit_custom_function', 'fit_predefined_custom']
    
    for method in methods:
        if hasattr(cls, method):
            print(f"✓ Method {method} exists")
        else:
            print(f"✗ Method {method} missing")
            
    print("Fix verification complete!")
    
except Exception as e:
    print(f"Error during verification: {e}")
