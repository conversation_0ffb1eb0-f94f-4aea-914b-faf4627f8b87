{"geminicodeassist.languages": ["python"], "geminicodeassist.project": "", "jupyter.interactiveWindow.creationMode": "perFile", "workbench.colorTheme": "Default Light Modern", "tabnine.experimentalAutoImports": true, "roo-cline.allowedCommands": ["npm test", "npm install", "tsc", "git log", "git diff", "git show"], "github.copilot.nextEditSuggestions.enabled": false, "github.copilot.enable": {"*": false, "plaintext": false, "markdown": false, "scminput": false, "python": true}, "diffEditor.maxComputationTime": 0, "mcp": {"inputs": [], "servers": {"mcp-server-time": {"command": "python", "args": ["-m", "mcp_server_time", "--local-timezone=Asia/Makassar"], "env": {}}, "Context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}}, "brave-search": {"command": "npx", "args": ["-y", "brave-search-mcp"], "env": {"BRAVE_API_KEY": "BSA3nkpkp05lMLWPCNPOFSYtfcnuSC3"}}}}, "workbench.settings.applyToAllProfiles": ["mcp"], "augment.nextEdit.enableBackgroundSuggestions": false, "kilo-code.allowedCommands": ["npm test", "npm install", "tsc", "git log", "git diff", "git show"], "chat.mcp.discovery.enabled": true, "basedpyright.disableLanguageServices": true}