import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
import pandas as pd
from scipy.optimize import curve_fit
from sympy import sympify, symbols, lambdify
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

class CurveFittingTool(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Curve Fitting Tool")
        self.geometry("800x600")

        # Initial sample data
        x = np.linspace(0, 50, 100)
        y = 2 * x + 3 + np.random.normal(0, 5, 100)
        z = 0.01 * x**3 - 0.5 * x**2 + 10 * x + 100 + np.random.normal(0, 100, 100)
        self.data = {'x': x, 'y': y, 'z': z}

        # --- Control Panel (Left) ---
        control_frame = tk.Frame(self)
        control_frame.pack(side=tk.LEFT, padx=10, pady=10, fill=tk.Y)

        # Select Data File Button
        self.select_file_button = tk.Button(control_frame, text="Select Data File", command=self.import_data)
        self.select_file_button.pack(pady=5)

        # Fit name
        tk.Label(control_frame, text="Fit name:").pack()
        self.fit_name_entry = tk.Entry(control_frame)
        self.fit_name_entry.pack()
        self.fit_name_entry.insert(0, "untitled fit 1")

        # Fit type
        tk.Label(control_frame, text="Fit type:").pack()
        self.fit_type_var = tk.StringVar(value="Polynomial")
        fit_types = ["Linear", "Power", "Exponential", "Polynomial", "Custom"]
        self.fit_type_dropdown = ttk.Combobox(control_frame, textvariable=self.fit_type_var, values=fit_types)
        self.fit_type_dropdown.pack()
        self.fit_type_dropdown.bind("<<ComboboxSelected>>", self.on_fit_type_change)

        # Custom function entry (initially hidden)
        self.custom_func_label = tk.Label(control_frame, text="Custom function (e.g., a*x + b):")
        self.custom_func_entry = tk.Entry(control_frame)
        self.custom_func_entry.insert(0, "a*x + b")

        # Degree (for polynomial)
        self.degree_label = tk.Label(control_frame, text="Degree:")
        self.degree_var = tk.StringVar(value="3")
        degrees = [str(i) for i in range(1, 6)]
        self.degree_dropdown = ttk.Combobox(control_frame, textvariable=self.degree_var, values=degrees)
        self.degree_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Auto fit checkbox
        self.auto_fit_var = tk.BooleanVar(value=True)
        self.auto_fit_check = tk.Checkbutton(control_frame, text="Auto fit", variable=self.auto_fit_var)
        self.auto_fit_check.pack()

        # X data
        tk.Label(control_frame, text="X data:").pack()
        self.x_data_var = tk.StringVar(value="x")
        self.x_data_dropdown = ttk.Combobox(control_frame, textvariable=self.x_data_var)
        self.x_data_dropdown['values'] = list(self.data.keys())
        self.x_data_dropdown.pack()
        self.x_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Y data
        tk.Label(control_frame, text="Y data:").pack()
        self.y_data_var = tk.StringVar(value="z")
        self.y_data_dropdown = ttk.Combobox(control_frame, textvariable=self.y_data_var)
        self.y_data_dropdown['values'] = list(self.data.keys())
        self.y_data_dropdown.pack()
        self.y_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Fit and Stop buttons
        self.fit_button = tk.Button(control_frame, text="Fit", command=self.perform_fit)
        self.fit_button.pack(pady=5)
        self.stop_button = tk.Button(control_frame, text="Stop", state="disabled")
        self.stop_button.pack()

        # Results frame
        results_frame = tk.LabelFrame(control_frame, text="Results")
        results_frame.pack(pady=10, fill=tk.X)
        self.equation_label = tk.Label(results_frame, text="", wraplength=200)
        self.equation_label.pack()
        self.param_labels = []
        for i in range(5):  # Up to 5 parameters
            label = tk.Label(results_frame, text="")
            label.pack()
            self.param_labels.append(label)
        self.sse_label = tk.Label(results_frame, text="SSE: ")
        self.sse_label.pack()
        self.rsquare_label = tk.Label(results_frame, text="R-square: ")
        self.rsquare_label.pack()
        self.rmse_label = tk.Label(results_frame, text="RMSE: ")
        self.rmse_label.pack()

        # --- Plot Frame (Right) ---
        plot_frame = tk.Frame(self)
        plot_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        self.figure = Figure(figsize=(5, 4), dpi=100)
        self.ax = self.figure.add_subplot(111)
        self.canvas = FigureCanvasTkAgg(self.figure, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Initial setup
        self.on_fit_type_change()  # Set initial visibility
        self.perform_fit()

    def parse_range(self, range_str, max_value, label):
        """Parse a range string (e.g., '1-3') and validate it."""
        if not range_str:
            return slice(None)  # Default to all if empty
        try:
            if '-' in range_str:
                start, end = map(int, range_str.split('-'))
                if start < 1 or end > max_value or start > end:
                    raise ValueError(f"Invalid {label} range. Must be between 1 and {max_value} and start <= end.")
                return slice(start - 1, end)  # Convert to 0-based indexing
            else:
                idx = int(range_str) - 1
                if idx < 0 or idx >= max_value:
                    raise ValueError(f"Invalid {label} index. Must be between 1 and {max_value}.")
                return slice(idx, idx + 1)
        except ValueError as e:
            messagebox.showerror("Error", str(e))
            return None

    def import_data(self):
        """Import data from Excel or CSV and allow range selection with preview"""
        file_path = filedialog.askopenfilename(
            title="Select Excel or CSV file",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("CSV files", "*.csv")]
        )
        if not file_path:
            return

        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
            else:
                # Excel file: select sheet
                excel_file = pd.ExcelFile(file_path)
                sheet_names = excel_file.sheet_names
                if not sheet_names:
                    messagebox.showerror("Error", "No sheets found in the Excel file.")
                    return
                sheet_name = tk.StringVar(value=sheet_names[0])
                sheet_dialog = tk.Toplevel(self)
                sheet_dialog.title("Select Sheet")
                tk.Label(sheet_dialog, text="Select sheet:").pack()
                sheet_dropdown = ttk.Combobox(sheet_dialog, textvariable=sheet_name, values=sheet_names)
                sheet_dropdown.pack()
                tk.Button(sheet_dialog, text="OK", command=sheet_dialog.destroy).pack()
                sheet_dialog.wait_window()
                selected_sheet = sheet_name.get()
                df = pd.read_excel(file_path, sheet_name=selected_sheet)

            # Create range selection dialog with preview
            range_dialog = tk.Toplevel(self)
            range_dialog.title("Select Data Ranges")
            range_dialog.geometry("600x400")

            # Preview Table
            preview_frame = ttk.Frame(range_dialog)
            preview_frame.pack(pady=10, fill=tk.BOTH, expand=True)
            preview_tree = ttk.Treeview(preview_frame, show="headings", height=10)
            preview_tree["columns"] = list(df.columns)
            for col in df.columns:
                preview_tree.heading(col, text=col)
                preview_tree.column(col, width=100)
            for idx, row in df.head(10).iterrows():  # Show first 10 rows as preview
                preview_tree.insert("", tk.END, values=list(row))
            preview_scroll = ttk.Scrollbar(preview_frame, orient="horizontal", command=preview_tree.xview)
            preview_tree.configure(xscrollcommand=preview_scroll.set)
            preview_tree.pack(side=tk.TOP, fill=tk.BOTH, expand=True)
            preview_scroll.pack(side=tk.BOTTOM, fill=tk.X)

            # X range inputs
            tk.Label(range_dialog, text="X Column Range (e.g., 1-3 or 2):").pack()
            x_col_range_entry = tk.Entry(range_dialog)
            x_col_range_entry.pack()
            tk.Label(range_dialog, text="X Row Range (e.g., 1-10 or 5):").pack()
            x_row_range_entry = tk.Entry(range_dialog)
            x_row_range_entry.pack()

            # Y range inputs
            tk.Label(range_dialog, text="Y Column Range (e.g., 1-3 or 2):").pack()
            y_col_range_entry = tk.Entry(range_dialog)
            y_col_range_entry.pack()
            tk.Label(range_dialog, text="Y Row Range (e.g., 1-10 or 5):").pack()
            y_row_range_entry = tk.Entry(range_dialog)
            y_row_range_entry.pack()

            def apply_ranges():
                x_col_range = self.parse_range(x_col_range_entry.get(), df.shape[1], "column")
                x_row_range = self.parse_range(x_row_range_entry.get(), df.shape[0], "row")
                y_col_range = self.parse_range(y_col_range_entry.get(), df.shape[1], "column")
                y_row_range = self.parse_range(y_row_range_entry.get(), df.shape[0], "row")

                if None in (x_col_range, x_row_range, y_col_range, y_row_range):
                    return

                # Extract X data
                x_df = df.iloc[x_row_range, x_col_range]
                if x_df.shape[1] != 1:
                    messagebox.showerror("Error", "Please select a single column for X data.")
                    return
                x_data = x_df.iloc[:, 0].values
                # Convert to numeric, coercing errors to NaN and dropping them
                x_data = pd.to_numeric(x_data, errors='coerce').dropna().values
                if len(x_data) == 0:
                    messagebox.showerror("Error", "X data contains no valid numeric values.")
                    return
                x_col_name = df.columns[x_col_range].tolist()[0]

                # Extract Y data
                y_df = df.iloc[y_row_range, y_col_range]
                if y_df.shape[1] != 1:
                    messagebox.showerror("Error", "Please select a single column for Y data.")
                    return
                y_data = y_df.iloc[:, 0].values
                # Convert to numeric, coercing errors to NaN and dropping them
                y_data = pd.to_numeric(y_data, errors='coerce').dropna().values
                if len(y_data) == 0:
                    messagebox.showerror("Error", "Y data contains no valid numeric values.")
                    return
                y_col_name = df.columns[y_col_range].tolist()[0]

                # Ensure X and Y have the same length after filtering non-numeric
                min_len = min(len(x_data), len(y_data))
                x_data = x_data[:min_len]
                y_data = y_data[:min_len]

                # Update data dictionary
                self.data.clear()
                self.data[x_col_name] = x_data
                self.data[y_col_name] = y_data

                # Update dropdowns
                self.x_data_dropdown['values'] = [x_col_name]
                self.y_data_dropdown['values'] = [y_col_name]
                self.x_data_var.set(x_col_name)
                self.y_data_var.set(y_col_name)
                self.perform_fit()
                range_dialog.destroy()

            tk.Button(range_dialog, text="Apply", command=apply_ranges).pack(pady=10)
            range_dialog.transient(self)
            range_dialog.grab_set()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load file: {e}")

    def on_fit_type_change(self, *args):
        """Show/hide custom function entry and degree dropdown based on fit type"""
        fit_type = self.fit_type_var.get()
        if fit_type == "Custom":
            self.custom_func_label.pack()
            self.custom_func_entry.pack()
            self.degree_label.pack_forget()
            self.degree_dropdown.pack_forget()
        elif fit_type == "Polynomial":
            self.custom_func_label.pack_forget()
            self.custom_func_entry.pack_forget()
            self.degree_label.pack()
            self.degree_dropdown.pack()
        else:
            self.custom_func_label.pack_forget()
            self.custom_func_entry.pack_forget()
            self.degree_label.pack_forget()
            self.degree_dropdown.pack_forget()
        if self.auto_fit_var.get():
            self.perform_fit()

    def on_param_change(self, *args):
        """Trigger fit if auto fit is enabled"""
        if self.auto_fit_var.get():
            self.perform_fit()

    def perform_fit(self):
        """Perform fitting based on selected type and update plot and results"""
        try:
            x_key = self.x_data_var.get()
            y_key = self.y_data_var.get()
            if x_key not in self.data or y_key not in self.data:
                return

            x = self.data[x_key]
            y = self.data[y_key]

            # Check for numeric data (already ensured during import)
            min_len = min(len(x), len(y))
            x = x[:min_len]
            y = y[:min_len]

            fit_type = self.fit_type_var.get()
            if fit_type == "Polynomial":
                degree = int(self.degree_var.get())
                coeffs = np.polyfit(x, y, degree)
                poly = np.poly1d(coeffs)
                fitted_y = poly(x)
                equation = f"Polynomial (degree {degree}): f(x) = " + " + ".join(
                    [f"{c:.2f}x^{degree-i}" if i < degree else f"{c:.2f}" for i, c in enumerate(coeffs)]
                )
                params = [f"p{i+1} = {c:.2f}" for i, c in enumerate(coeffs)]
            elif fit_type == "Custom":
                func_str = self.custom_func_entry.get()
                try:
                    params = symbols('a b c d e')
                    x_sym = symbols('x')
                    expr = sympify(func_str)
                    model = lambdify((x_sym, *params), expr, "numpy")
                    param_names = [str(p) for p in params if p in expr.free_symbols]
                    num_params = len(param_names)
                    p0 = [1.0] * num_params
                    popt, _ = curve_fit(model, x, y, p0=p0, maxfev=10000)
                    fitted_y = model(x, *popt)
                    equation = f"Custom: f(x) = {func_str}"
                    params = [f"{name} = {val:.2f}" for name, val in zip(param_names, popt)]
                except Exception as e:
                    messagebox.showerror("Error", f"Invalid custom function: {e}")
                    return
            else:
                if fit_type == "Linear":
                    def model(x, a, b):
                        return a * x + b
                    equation_template = "Linear: f(x) = a x + b"
                    param_names = ["a", "b"]
                elif fit_type == "Power":
                    def model(x, a, b):
                        return a * x**b
                    equation_template = "Power: f(x) = a x^b"
                    param_names = ["a", "b"]
                elif fit_type == "Exponential":
                    def model(x, a, b):
                        return a * np.exp(b * x)
                    equation_template = "Exponential: f(x) = a e^(b x)"
                    param_names = ["a", "b"]

                popt, _ = curve_fit(model, x, y, maxfev=10000)
                fitted_y = model(x, *popt)
                equation = equation_template.replace("a", f"{popt[0]:.2f}").replace("b", f"{popt[1]:.2f}")
                params = [f"{name} = {val:.2f}" for name, val in zip(param_names, popt)]

            self.equation_label.config(text=equation)
            for i, param in enumerate(params):
                self.param_labels[i].config(text=param)
            for i in range(len(params), 5):
                self.param_labels[i].config(text="")

            residuals = y - fitted_y
            sse = np.sum(residuals**2)
            sst = np.sum((y - np.mean(y))**2)
            rsquare = 1 - (sse / sst) if sst != 0 else 0
            rmse = np.sqrt(sse / len(y))
            self.sse_label.config(text=f"SSE: {sse:.2f}")
            self.rsquare_label.config(text=f"R-square: {rsquare:.4f}")
            self.rmse_label.config(text=f"RMSE: {rmse:.2f}")

            self.ax.clear()
            self.ax.scatter(x, y, color='black', label='Data')
            x_plot = np.linspace(min(x), max(x), 100)
            if fit_type == "Polynomial":
                y_plot = poly(x_plot)
            elif fit_type == "Custom":
                y_plot = model(x_plot, *popt)
            else:
                y_plot = model(x_plot, *popt)
            self.ax.plot(x_plot, y_plot, 'b-', label='Fit')
            self.ax.grid(True)
            self.ax.set_xlim(min(x), max(x))
            self.ax.set_ylim(min(y), max(y))
            self.ax.legend()
            self.canvas.draw()

        except Exception as e:
            messagebox.showerror("Error", f"Fitting failed: {e}")

if __name__ == "__main__":
    app = CurveFittingTool()
    app.mainloop()