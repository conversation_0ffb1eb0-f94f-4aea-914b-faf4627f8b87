# -*- coding: utf-8 -*-
"""
Created on Sat Mar 15 11:40:13 2024

@author: mutia
"""

import sys
import os
import numpy as np
import pandas as pd
import traceback
import warnings
from PyQt5.QtWidgets import (QMainWindow, QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QComboBox, QPushButton, QLineEdit, QCheckBox,
                             QGroupBox, QGridLayout, QFileDialog, QMessageBox, QDialog,
                             QVBoxLayout as QVBoxLayoutDialog, QHBoxLayout as QHBoxLayoutDialog,
                             QTableWidget, QTableWidgetItem, QScrollArea, QDialogButtonBox,
                             QSpinBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from scipy.optimize import curve_fit
from sympy import sympify, symbols, lambdify
import pyqtgraph as pg

class ImportDataDialog(QDialog):
    def __init__(self, parent, df):
        super().__init__(parent)
        self.setWindowTitle("Select Data Ranges")
        self.setGeometry(100, 100, 800, 500)
        self.df = df

        layout = QVBoxLayoutDialog(self)
        preview_frame = QWidget()
        preview_layout = QVBoxLayoutDialog(preview_frame)
        self.table = QTableWidget()
        self.table.setRowCount(len(df))
        self.table.setColumnCount(len(df.columns) + 1)
        self.table.setHorizontalHeaderLabels(["Row"] + [f"{i+1}: {col}" for i, col in enumerate(df.columns)])
        self.table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.table.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOn)

        for i in range(len(df)):
            self.table.setItem(i, 0, QTableWidgetItem(str(i + 1)))
            for j, value in enumerate(df.iloc[i]):
                self.table.setItem(i, j + 1, QTableWidgetItem(str(value)))

        self.table.resizeColumnsToContents()
        preview_layout.addWidget(self.table)
        scroll = QScrollArea()
        scroll.setWidget(preview_frame)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)

        layout.addWidget(QLabel("Row Range (e.g., 1-10 or leave blank for all):"))
        self.row_range_entry = QLineEdit()
        layout.addWidget(self.row_range_entry)

        layout.addWidget(QLabel("X Column:"))
        layout.addWidget(QLabel("Note: Column A = 1, B = 2, etc."))
        self.x_col_var = QComboBox()
        self.x_col_var.addItems([f"{i+1}: {col}" for i, col in enumerate(df.columns)])
        first_numeric_idx = self.find_first_numeric_column(df)
        self.x_col_var.setCurrentIndex(first_numeric_idx)
        layout.addWidget(self.x_col_var)

        layout.addWidget(QLabel("Y Column:"))
        layout.addWidget(QLabel("Note: Column A = 1, B = 2, etc."))
        self.y_col_var = QComboBox()
        self.y_col_var.addItems([f"{i+1}: {col}" for i, col in enumerate(df.columns)])
        second_numeric_idx = min(first_numeric_idx + 1, len(df.columns) - 1)
        if not pd.to_numeric(df.iloc[:, second_numeric_idx], errors='coerce').notna().any():
            second_numeric_idx = first_numeric_idx
        self.y_col_var.setCurrentIndex(second_numeric_idx)
        layout.addWidget(self.y_col_var)

        layout.addWidget(QLabel("Z Column (color key, optional):"))
        layout.addWidget(QLabel("Select 'None' to use a single color."))
        self.z_col_var = QComboBox()
        self.z_col_var.addItems(["None"] + [f"{i+1}: {col}" for i, col in enumerate(df.columns)])
        self.z_col_var.setCurrentIndex(0)
        layout.addWidget(self.z_col_var)

        layout.addWidget(QLabel("Index Column (optional for point labels):"))
        layout.addWidget(QLabel("Select 'None' to disable point labels."))
        self.index_col_var = QComboBox()
        self.index_col_var.addItems(["None"] + [f"{i+1}: {col}" for i, col in enumerate(df.columns)])
        self.index_col_var.setCurrentIndex(0)
        layout.addWidget(self.index_col_var)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def find_first_numeric_column(self, df):
        for i, col in enumerate(df.columns):
            if pd.to_numeric(df[col], errors='coerce').notna().any():
                return i
        return 0

    def get_selected_data(self):
        row_range = self.row_range_entry.text()
        x_col = self.x_col_var.currentText().split(":")[0].strip()
        y_col = self.y_col_var.currentText().split(":")[0].strip()
        z_col = self.z_col_var.currentText()
        index_col = self.index_col_var.currentText()
        return row_range, x_col, y_col, z_col, index_col

class CurveFittingTool(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Curve Fitting Tool")
        self.setGeometry(100, 100, 900, 700)

        x = np.linspace(0, 50, 100)
        y = 2 * x + 3 + np.random.normal(0, 5, 100)
        z = np.random.choice(['A', 'B', 'C'], 100)
        index = np.arange(100)
        self.data = {'x': x, 'y': y, 'z': z, 'index': index}

        self.custom_functions = {
            "Gaussian": {
                "model": lambda x, a, b, c: a * np.exp(-(x - b)**2 / (2 * c**2)),
                "equation": "Gaussian: f(x) = {a} * exp(-(x - {b})^2 / (2 * {c}^2))",
                "params": ["a", "b", "c"],
                "initial_guess": lambda x, y: [
                    np.max(y),                  # a: peak height
                    x[np.argmax(y)],           # b: peak position (x value at maximum y)
                    (np.max(x) - np.min(x))/5   # c: width estimate (1/5 of data range)
                ],
                "bounds": ([0, -np.inf, 0], [np.inf, np.inf, np.inf])  # a>0, c>0 (width must be positive)
            },
            "Gompertz": {
                "model": lambda x, a, b, c: a * np.exp(-b * np.exp(-c * x)),
                "equation": "Gompertz: f(x) = {a} * exp(-{b} * exp(-{c} * x))",
                "params": ["a", "b", "c"],
                "initial_guess": lambda x, y: [
                    np.max(y) * 1.2,  # a: asymptotic value (slightly higher than max observed)
                    1.0,              # b: displacement
                    0.1               # c: growth rate
                ],
                "bounds": ([0, 0, 0], [np.inf, np.inf, np.inf])  # all parameters typically positive
            },
            "Sine Wave": {
                "model": lambda x, a, b, c, d: a * np.sin(b * x + c) + d,
                "equation": "Sine Wave: f(x) = {a} * sin({b} * x + {c}) + {d}",
                "params": ["a", "b", "c", "d"],
                "initial_guess": lambda x, y: [
                    (np.max(y) - np.min(y))/2,                # a: amplitude
                    2 * np.pi / (np.max(x) - np.min(x)) * 2, # b: frequency (assume 2 cycles)
                    0,                                        # c: phase shift
                    np.mean(y)                               # d: vertical shift
                ],
                "bounds": ([-np.inf, 0, -2*np.pi, -np.inf], [np.inf, np.inf, 2*np.pi, np.inf])  # b>0, -2π≤c≤2π
            },
            "Logistic Growth": {
                "model": lambda x, a, b, c: a / (1 + b * np.exp(-c * x)),
                "equation": "Logistic Growth: f(x) = {a} / (1 + {b} * exp(-{c} * x))",
                "params": ["a", "b", "c"],
                "initial_guess": lambda x, y: [
                    np.max(y) * 1.1,  # a: upper asymptote (slightly above max observed)
                    1.0,              # b: shifts curve right (positive) or left (negative)
                    0.1               # c: growth rate
                ],
                "bounds": ([0, 0, 0], [np.inf, np.inf, np.inf])  # typically all positive
            }
        }

        self.x_min = 0
        self.x_max = 50
        self.y_min = -10
        self.y_max = 120
        self.point_size = 10
        self.colormap = 'viridis'

        main_widget = QWidget(self)
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)

        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        main_layout.addWidget(control_widget)

        self.select_file_btn = QPushButton("Select Data File")
        self.select_file_btn.clicked.connect(self.import_data)
        control_layout.addWidget(self.select_file_btn)

        control_layout.addWidget(QLabel("Fit name:"))
        self.fit_name_edit = QLineEdit("untitled fit 1")
        control_layout.addWidget(self.fit_name_edit)

        control_layout.addWidget(QLabel("Fit type:"))
        self.fit_type_combo = QComboBox()
        self.fit_type_combo.addItems(["Linear", "Power", "Exponential", "Polynomial", "Custom", "Pre-defined Custom"])
        self.fit_type_combo.currentTextChanged.connect(self.on_fit_type_change)
        control_layout.addWidget(self.fit_type_combo)

        self.custom_func_label = QLabel("Custom function (e.g., a*x + b):")
        self.custom_func_edit = QLineEdit("a*x + b")
        self.custom_func_widget = QWidget()
        custom_layout = QVBoxLayout(self.custom_func_widget)
        custom_layout.addWidget(self.custom_func_label)
        custom_layout.addWidget(self.custom_func_edit)

        self.predefined_func_label = QLabel("Pre-defined function:")
        self.predefined_func_combo = QComboBox()
        self.predefined_func_combo.addItems(list(self.custom_functions.keys()))
        self.predefined_func_widget = QWidget()
        predefined_layout = QVBoxLayout(self.predefined_func_widget)
        predefined_layout.addWidget(self.predefined_func_label)
        predefined_layout.addWidget(self.predefined_func_combo)

        self.degree_label = QLabel("Degree:")
        self.degree_combo = QComboBox()
        self.degree_combo.addItems([str(i) for i in range(1, 6)])
        self.degree_combo.setCurrentText("3")
        self.degree_combo.currentTextChanged.connect(self.on_param_change)
        self.degree_widget = QWidget()
        degree_layout = QVBoxLayout(self.degree_widget)
        degree_layout.addWidget(self.degree_label)
        degree_layout.addWidget(self.degree_combo)

        # Exponential version dropdown (hidden by default)
        self.exponential_version_label = QLabel("Exponential version:")
        self.exponential_version_combo = QComboBox()
        self.exponential_version_combo.addItems([
            "Standard (a * exp(bx))",
            "Generalized (A * exp(kx))",
            "Base b (A * b^x)",
            "Decay with Asymptote (A * exp(-kx) + C)",
            "Two-Term (A*e^(f*x) + B*e^(g*x))"
        ])
        self.exponential_version_combo.currentIndexChanged.connect(self.on_param_change)
        self.exponential_version_widget = QWidget()
        exponential_layout = QVBoxLayout(self.exponential_version_widget)
        exponential_layout.addWidget(self.exponential_version_label)
        exponential_layout.addWidget(self.exponential_version_combo)

        # Power version dropdown (hidden by default)
        self.power_version_label = QLabel("Power function version:")
        self.power_version_combo = QComboBox()
        self.power_version_combo.addItems([
            "Standard (a * x^b)",
            "With Asymptote (c + k * x^a)"
        ])
        self.power_version_combo.currentIndexChanged.connect(self.on_param_change)
        self.power_version_widget = QWidget()
        power_layout = QVBoxLayout(self.power_version_widget)
        power_layout.setContentsMargins(0,0,0,0) # Match other similar widgets
        power_layout.addWidget(self.power_version_label)
        power_layout.addWidget(self.power_version_combo)

        # Add to main control layout
        control_layout.addWidget(self.custom_func_widget)
        control_layout.addWidget(self.predefined_func_widget)
        control_layout.addWidget(self.exponential_version_widget)
        control_layout.addWidget(self.power_version_widget)
        control_layout.addWidget(self.degree_widget)

        self.auto_fit_check = QCheckBox("Auto fit")
        self.auto_fit_check.stateChanged.connect(self.on_param_change)
        control_layout.addWidget(self.auto_fit_check)

        self.show_uncertainty_check = QCheckBox("Show uncertainty (±2*SER)")
        self.show_uncertainty_check.setChecked(True)
        self.show_uncertainty_check.stateChanged.connect(self.perform_fit) # Connect to perform_fit
        control_layout.addWidget(self.show_uncertainty_check)

        self.show_labels_check = QCheckBox("Show Point Labels")
        self.show_labels_check.setChecked(True)
        self.show_labels_check.stateChanged.connect(self.perform_fit) # Connect to perform_fit
        control_layout.addWidget(self.show_labels_check)

        self.show_equation_legend_check = QCheckBox("Show Equation in Plot")
        self.show_equation_legend_check.setChecked(False)
        self.show_equation_legend_check.stateChanged.connect(self.perform_fit) # Connect to perform_fit
        control_layout.addWidget(self.show_equation_legend_check)

        # Data selection dropdowns
        self.x_data_combo = QComboBox()
        self.y_data_combo = QComboBox()
        self.z_data_combo = QComboBox()
        self.index_data_combo = QComboBox()
        # Connect on_param_change for x, y, z, index combos
        for combo in [self.x_data_combo, self.y_data_combo]:
            combo.currentTextChanged.connect(self.on_param_change)
        # z_data_combo and index_data_combo might also trigger re-fit/re-plot
        for combo in [self.z_data_combo, self.index_data_combo]: # Keep 6th's connection style
            combo.currentTextChanged.connect(self.on_param_change)
        # Add to layout
        control_layout.addWidget(QLabel("X data:"))
        control_layout.addWidget(self.x_data_combo)
        control_layout.addWidget(QLabel("Y data:"))
        control_layout.addWidget(self.y_data_combo)
        control_layout.addWidget(QLabel("Z data (color key):"))
        control_layout.addWidget(self.z_data_combo)
        control_layout.addWidget(QLabel("Index data:"))
        control_layout.addWidget(self.index_data_combo)

        self.fit_btn = QPushButton("Fit")
        self.fit_btn.clicked.connect(self.perform_fit)
        control_layout.addWidget(self.fit_btn)

        # Advanced Fitting Options GroupBox
        advanced_fitting_group = QGroupBox("Advanced Fitting Options")
        advanced_fitting_layout = QGridLayout(advanced_fitting_group)

        advanced_fitting_layout.addWidget(QLabel("Max Iterations:"), 0, 0)
        self.max_iter_spin = QSpinBox()
        self.max_iter_spin.setRange(100, 100000)
        self.max_iter_spin.setValue(10000)
        self.max_iter_spin.valueChanged.connect(self.on_param_change)
        advanced_fitting_layout.addWidget(self.max_iter_spin, 0, 1)

        advanced_fitting_layout.addWidget(QLabel("Fit Method:"), 1, 0)
        self.fit_method_combo = QComboBox()
        self.fit_method_combo.addItems(["lm", "trf", "dogbox"])
        self.fit_method_combo.currentTextChanged.connect(self.on_param_change)
        advanced_fitting_layout.addWidget(self.fit_method_combo, 1, 1)

        self.use_smart_guess_check = QCheckBox("Use Smart Initial Guesses")
        self.use_smart_guess_check.setChecked(True) # Default to true as 6th version has smart guesses
        self.use_smart_guess_check.stateChanged.connect(self.on_param_change)
        advanced_fitting_layout.addWidget(self.use_smart_guess_check, 2, 0, 1, 2)

        self.use_bounds_check = QCheckBox("Use Parameter Bounds (if defined)")
        self.use_bounds_check.setChecked(True) # Default to true as 6th version has bounds
        self.use_bounds_check.stateChanged.connect(self.on_param_change)
        advanced_fitting_layout.addWidget(self.use_bounds_check, 3, 0, 1, 2)
        control_layout.addWidget(advanced_fitting_group)

        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)
        self.equation_label = QLabel("")
        self.param_labels = [QLabel("") for _ in range(5)] # Max 5 params displayed, can be adjusted
        self.sse_label = QLabel("SSE: ")
        self.rsquare_label = QLabel("R-square: ")
        self.rmse_label = QLabel("RMSE: ")
        self.ser_label = QLabel("SER: ")
        for label in [self.equation_label] + self.param_labels + [self.sse_label, self.rsquare_label, self.rmse_label, self.ser_label]:
            results_layout.addWidget(label)
        control_layout.addWidget(results_group)

        settings_group = QGroupBox("Plot Settings")
        settings_layout = QGridLayout(settings_group)
        self.x_min_edit = QLineEdit(str(self.x_min))
        self.x_max_edit = QLineEdit(str(self.x_max))
        self.y_min_edit = QLineEdit(str(self.y_min))
        self.y_max_edit = QLineEdit(str(self.y_max))
        self.point_size_edit = QLineEdit(str(self.point_size))
        self.colormap_combo = QComboBox()
        self.colormap_combo.addItems(['viridis', 'plasma', 'inferno', 'magma', 'cividis'])
        self.colormap_combo.setCurrentText(self.colormap)
        self.tick_step_edit = QLineEdit("5") # Default from 5th

        self.point_shape_combo = QComboBox()
        self.point_shape_combo.addItems(['Circle', 'Square', 'Diamond', 'Triangle', 'Star', 'Pentagon', 'Hexagon'])
        self.point_shape_combo.setCurrentText('Circle')
        
        self.single_color_combo = QComboBox()
        self.single_color_combo.addItems(['Blue', 'Red', 'Green', 'Black', 'Cyan', 'Light Gray', 'Magenta', 'Yellow', 'Orange', 'Purple'])
        self.single_color_combo.setCurrentText('Blue')
        self.single_color_combo.currentTextChanged.connect(self.perform_fit) # Connect to perform_fit

        settings_layout.addWidget(QLabel("X Min:"), 0, 0)
        settings_layout.addWidget(self.x_min_edit, 0, 1)
        settings_layout.addWidget(QLabel("X Max:"), 0, 2)
        settings_layout.addWidget(self.x_max_edit, 0, 3)
        settings_layout.addWidget(QLabel("Y Min:"), 1, 0)
        settings_layout.addWidget(self.y_min_edit, 1, 1)
        settings_layout.addWidget(QLabel("Y Max:"), 1, 2)
        settings_layout.addWidget(self.y_max_edit, 1, 3)
        settings_layout.addWidget(QLabel("Point Size:"), 2, 0)
        settings_layout.addWidget(self.point_size_edit, 2, 1)
        settings_layout.addWidget(QLabel("Point Shape:"), 2, 2)
        settings_layout.addWidget(self.point_shape_combo, 2, 3)
        settings_layout.addWidget(QLabel("Colormap:"), 3, 0)
        settings_layout.addWidget(self.colormap_combo, 3, 1)
        settings_layout.addWidget(QLabel("Single Color:"), 3, 2)
        settings_layout.addWidget(self.single_color_combo, 3, 3)
        settings_layout.addWidget(QLabel("Tick Step:"), 4, 0)
        settings_layout.addWidget(self.tick_step_edit, 4, 1)

        self.axis_title_font_type = QComboBox()
        self.axis_title_font_type.addItems(["Arial", "Times New Roman", "Courier New"])
        self.axis_title_font_size = QSpinBox()
        self.axis_title_font_size.setRange(8, 20)
        self.axis_title_font_size.setValue(12)
        self.axis_tick_font_type = QComboBox()
        self.axis_tick_font_type.addItems(["Arial", "Times New Roman", "Courier New"])
        self.axis_tick_font_size = QSpinBox()
        self.axis_tick_font_size.setRange(8, 20)
        self.axis_tick_font_size.setValue(10)

        settings_layout.addWidget(QLabel("Axis Title Font Type:"), 5, 0)
        settings_layout.addWidget(self.axis_title_font_type, 5, 1)
        settings_layout.addWidget(QLabel("Axis Title Font Size:"), 5, 2)
        settings_layout.addWidget(self.axis_title_font_size, 5, 3)
        settings_layout.addWidget(QLabel("Axis Tick Font Type:"), 6, 0)
        settings_layout.addWidget(self.axis_tick_font_type, 6, 1)
        settings_layout.addWidget(QLabel("Axis Tick Font Size:"), 6, 2)
        settings_layout.addWidget(self.axis_tick_font_size, 6, 3)

        self.axis_title_bold_check = QCheckBox("Bold")
        self.axis_title_italic_check = QCheckBox("Italic")
        settings_layout.addWidget(self.axis_title_bold_check, 5, 4)
        settings_layout.addWidget(self.axis_title_italic_check, 5, 5)

        self.axis_tick_bold_check = QCheckBox("Bold") 
        self.axis_tick_italic_check = QCheckBox("Italic")
        settings_layout.addWidget(self.axis_tick_bold_check, 6, 4)
        settings_layout.addWidget(self.axis_tick_italic_check, 6, 5)

        self.x_title_edit = QLineEdit("X")
        self.y_title_edit = QLineEdit("Y")
        settings_layout.addWidget(QLabel("X-axis Title:"), 7, 0)
        settings_layout.addWidget(self.x_title_edit, 7, 1)
        settings_layout.addWidget(QLabel("Y-axis Title:"), 7, 2)
        settings_layout.addWidget(self.y_title_edit, 7, 3)

        self.background_color_combo = QComboBox()
        self.background_color_combo.addItems(["White", "Black", "Light Gray", "Dark Gray", "Red", "Green", "Blue"])
        self.background_color_combo.setCurrentText("White")
        settings_layout.addWidget(QLabel("Background color:"), 8, 0)
        settings_layout.addWidget(self.background_color_combo, 8, 1)

        self.axis_color_combo = QComboBox()
        self.axis_color_combo.addItems(["Black", "White", "Red", "Green", "Blue", "Gray", "Dark Gray", "Light Gray"])
        self.axis_color_combo.setCurrentText("Light Gray")
        settings_layout.addWidget(QLabel("Axis color:"), 8, 2)
        settings_layout.addWidget(self.axis_color_combo, 8, 3)

        self.tick_label_color_combo = QComboBox()
        self.tick_label_color_combo.addItems(["Black", "White", "Red", "Green", "Blue", "Gray", "Dark Gray", "Light Gray"])
        self.tick_label_color_combo.setCurrentText("Light Gray")
        settings_layout.addWidget(QLabel("Tick label color:"), 9, 0)
        settings_layout.addWidget(self.tick_label_color_combo, 9, 1)

        self.apply_settings_btn = QPushButton("Apply Settings")
        self.apply_settings_btn.clicked.connect(self.apply_plot_settings)
        settings_layout.addWidget(self.apply_settings_btn, 10, 0, 1, 4) # Span across 4 columns
        control_layout.addWidget(settings_group)

        control_layout.addStretch()

        self.plot_widget = pg.PlotWidget()
        main_layout.addWidget(self.plot_widget, stretch=1)

        # Initialize with default/sample data columns
        self.update_data_columns()
        self.on_fit_type_change() # Set initial visibility of fit-specific widgets
        self.perform_fit()        # Plot initial data or fit
        if hasattr(self, 'colormap_combo'): # Ensure it exists before connecting
            self.colormap_combo.currentTextChanged.connect(self.perform_fit)

    def on_fit_type_change(self):
        fit_type = self.fit_type_combo.currentText()
        self.custom_func_widget.setVisible(fit_type == "Custom")
        self.predefined_func_widget.setVisible(fit_type == "Pre-defined Custom")
        self.exponential_version_widget.setVisible(fit_type == "Exponential")
        self.power_version_widget.setVisible(fit_type == "Power")
        self.degree_widget.setVisible(fit_type == "Polynomial")
        if self.auto_fit_check.isChecked():
            self.perform_fit()

    def on_param_change(self):
        try:
            if self.auto_fit_check.isChecked():
                self.perform_fit()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Parameter change failed: {e}\n{traceback.format_exc()}")

    def parse_range(self, range_str, max_value, label): # Add from 5th, used by import_data
        if not range_str:
            return slice(None)
        try:
            if '-' in range_str:
                start, end = map(int, range_str.split('-'))
                if start < 1 or end > max_value or start > end:
                    raise ValueError(f"Invalid {label} range. Must be between 1 and {max_value} and start <= end.")
                return slice(start - 1, end)
            else:
                idx = int(range_str) - 1
                if idx < 0 or idx >= max_value:
                    raise ValueError(f"Invalid {label} index. Must be between 1 and {max_value}.")
                return slice(idx, idx + 1)
        except ValueError as e:
            QMessageBox.critical(self, "Error", str(e))
            return None

    def import_data(self):
        try:
            file_path, _ = QFileDialog.getOpenFileName(
                self, "Select Data File", "", "Excel/CSV Files (*.xlsx *.xls *.csv *.txt)" # Adjusted filter
            )
            if not file_path:
                return  # User canceled
                
            file_ext = os.path.splitext(file_path)[1].lower()
            df = None

            if file_ext == '.csv' or file_ext == '.txt': # Treat txt as potential csv
                df = pd.read_csv(file_path)
                if file_ext == '.txt': # For txt, try to guess delimiter if simple read fails or has one col
                    if df.shape[1] == 1:
                        try:
                            df_guessed = pd.read_csv(file_path, sep=None, engine='python')
                            if df_guessed.shape[1] > 1:
                                df = df_guessed
                        except Exception:
                            pass # Stick with the original df if guessing fails

            elif file_ext in ['.xlsx', '.xls']:
                excel_file = pd.ExcelFile(file_path)
                sheet_names = excel_file.sheet_names
                if not sheet_names:
                    QMessageBox.critical(self, "Error", "No sheets found in the Excel file.")
                    return
                
                if len(sheet_names) == 1:
                    df = pd.read_excel(file_path, sheet_name=sheet_names[0])
                else:
                    sheet_dialog = QDialog(self)
                    sheet_dialog.setWindowTitle("Select Sheet")
                    layout = QVBoxLayoutDialog(sheet_dialog)
                    sheet_var = QComboBox()
                    sheet_var.addItems(sheet_names)
                    sheet_var.setCurrentIndex(0)
                    layout.addWidget(QLabel("Select sheet:"))
                    layout.addWidget(sheet_var)
                    buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
                    buttons.accepted.connect(sheet_dialog.accept)
                    buttons.rejected.connect(sheet_dialog.reject)
                    layout.addWidget(buttons)
                    if sheet_dialog.exec_() == QDialog.Accepted:
                        selected_sheet = sheet_var.currentText()
                        df = pd.read_excel(file_path, sheet_name=selected_sheet)
                    else:
                        return # User cancelled sheet selection
            else:
                QMessageBox.warning(self, "Unsupported Format", "The selected file format is not supported.")
                return

            if df is None: return

            dialog = ImportDataDialog(self, df)
            if (dialog.exec_() == QDialog.Accepted):
                row_range, x_col_num_str, y_col_num_str, z_col_str, index_col_str = dialog.get_selected_data()

                row_slice = self.parse_range(row_range, df.shape[0], "row")
                if row_slice is None: return

                x_col_idx = int(x_col_num_str) - 1
                y_col_idx = int(y_col_num_str) - 1
                z_col_idx = int(z_col_str.split(":")[0].strip()) - 1 if z_col_str != "None" else None
                index_col_idx = int(index_col_str.split(":")[0].strip()) - 1 if index_col_str != "None" else None

                # Extract data based on indices
                x_data = pd.to_numeric(df.iloc[row_slice, x_col_idx], errors='coerce').values
                y_data = pd.to_numeric(df.iloc[row_slice, y_col_idx], errors='coerce').values
                
                z_data = None
                if z_col_idx is not None:
                    z_data_raw = df.iloc[row_slice, z_col_idx].values
                    z_data_numeric_try = pd.to_numeric(z_data_raw, errors='coerce')
                    if np.all(np.isnan(z_data_numeric_try)): z_data = z_data_raw.astype(str)
                    else: z_data = z_data_numeric_try

                index_data = df.iloc[row_slice, index_col_idx].values if index_col_idx is not None else None

                valid_mask = ~np.isnan(x_data) & ~np.isnan(y_data) & ~np.isinf(x_data) & ~np.isinf(y_data)
                x_data, y_data = x_data[valid_mask], y_data[valid_mask]
                if z_data is not None: z_data = z_data[valid_mask]
                if index_data is not None: index_data = index_data[valid_mask]

                if len(x_data) == 0:
                    QMessageBox.critical(self, "Error", "No valid numeric data in selected X/Y columns or range.")
                    return

                x_col_name, y_col_name = df.columns[x_col_idx], df.columns[y_col_idx]
                z_col_name = df.columns[z_col_idx] if z_col_idx is not None else None
                index_col_name = df.columns[index_col_idx] if index_col_idx is not None else None

                self.data.clear()
                self.data[x_col_name] = x_data
                self.data[y_col_name] = y_data
                if z_col_name: self.data[z_col_name] = z_data
                if index_col_name: self.data[index_col_name] = index_data

                self.update_data_columns() # This will now use the filtered self.data

                # Set current text for combos based on imported column names
                self.x_data_combo.setCurrentText(x_col_name)
                self.y_data_combo.setCurrentText(y_col_name)
                self.z_data_combo.setCurrentText(z_col_name if z_col_name else "None")
                self.index_data_combo.setCurrentText(index_col_name if index_col_name else "None") # Default to None if not selected

                self.perform_fit()
                QMessageBox.information(self, "Data Imported", f"Successfully imported and processed data from {os.path.basename(file_path)}")
        except Exception as e:
            QMessageBox.critical(self, "Import Error", f"Failed to import data: {str(e)}")
    
    def update_data_columns(self):
        """Update dropdown menus with available data columns"""
        # Save current selections if possible
        current_x = self.x_data_combo.currentText()
        current_y = self.y_data_combo.currentText()
        current_z = self.z_data_combo.currentText()
        current_index = self.index_data_combo.currentText()
        
        # Clear and update dropdowns
        self.x_data_combo.clear()
        self.y_data_combo.clear()
        self.z_data_combo.clear()
        self.index_data_combo.clear()
        
        # Add new items
        if self.data:
            columns = list(self.data.keys())
            self.x_data_combo.addItems(columns)
            self.y_data_combo.addItems(columns)
            
            # Z and Index are optional
            self.z_data_combo.addItem("None")
            self.z_data_combo.addItems(columns)
            
            self.index_data_combo.addItem("None")
            self.index_data_combo.addItems(columns)
            
            # Try to restore previous selections
            if current_x in columns:
                self.x_data_combo.setCurrentText(current_x)
            if current_y in columns:
                self.y_data_combo.setCurrentText(current_y)
            if current_z in ["None"] + columns:
                self.z_data_combo.setCurrentText(current_z)
            if current_index in ["None"] + columns:
                self.index_data_combo.setCurrentText(current_index)
    
    def apply_plot_settings(self): # Copied from 5th_run_toolbox_PyQT_final.py
        try:
            # Validate numeric inputs
            try:
                x_min = float(self.x_min_edit.text())
                x_max = float(self.x_max_edit.text())
                y_min = float(self.y_min_edit.text())
                y_max = float(self.y_max_edit.text())
                point_size = float(self.point_size_edit.text())
                tick_step = float(self.tick_step_edit.text())
            except ValueError:
                QMessageBox.critical(self, "Error", "Please enter valid numeric values for plot settings.")
                return

            if x_min >= x_max or y_min >= y_max:
                QMessageBox.critical(self, "Error", "X and Y ranges must have min < max.")
                return
            if point_size <= 0 or tick_step <= 0:
                QMessageBox.critical(self, "Error", "Point size and tick step must be positive values.")
                return

            background_color = self.background_color_combo.currentText().lower().replace(" ", "")
            self.plot_widget.setBackground(background_color)

            font_family = self.axis_title_font_type.currentText()
            font_size = self.axis_title_font_size.value()
            x_text = self.x_title_edit.text()
            y_text = self.y_title_edit.text()

            title_style = f"font-family:{font_family}; font-size:{font_size}pt;"
            if self.axis_title_bold_check.isChecked():
                title_style += " font-weight:bold;"
            if self.axis_title_italic_check.isChecked():
                title_style += " font-style:italic;"

            x_html = f'<span style="{title_style}">{x_text}</span>'
            y_html = f'<span style="{title_style}">{y_text}</span>'

            self.plot_widget.setLabel('bottom', x_html)
            self.plot_widget.setLabel('left', y_html)

            axis_tick_font = QFont(self.axis_tick_font_type.currentText())
            axis_tick_font.setPointSize(self.axis_tick_font_size.value())
            axis_tick_font.setBold(self.axis_tick_bold_check.isChecked())
            axis_tick_font.setItalic(self.axis_tick_italic_check.isChecked())

            tick_color_name = self.tick_label_color_combo.currentText().lower().replace(" ", "")
            tick_color = self.get_color_from_name(tick_color_name) if tick_color_name != "lightgray" else (200,200,200)


            bottom_axis = self.plot_widget.getAxis('bottom')
            left_axis = self.plot_widget.getAxis('left')
            bottom_axis.setStyle(tickFont=axis_tick_font)
            left_axis.setStyle(tickFont=axis_tick_font)
            # For pyqtgraph, setPen on axis controls line color, setTextPen controls text color
            bottom_axis.setTextPen(pg.mkPen(color=tick_color)) 
            left_axis.setTextPen(pg.mkPen(color=tick_color))

            x_ticks = np.arange(x_min, x_max + tick_step, tick_step)
            y_ticks = np.arange(y_min, y_max + tick_step, tick_step)
            bottom_axis.setTicks([[(v, f"{v:.2f}") for v in x_ticks]])
            left_axis.setTicks([[(v, f"{v:.2f}") for v in y_ticks]])

            axis_color_name = self.axis_color_combo.currentText().lower().replace(" ", "")
            axis_color = self.get_color_from_name(axis_color_name) if axis_color_name != "lightgray" else (200,200,200)
            
            bottom_axis.setPen(pg.mkPen(color=axis_color, width=1))
            left_axis.setPen(pg.mkPen(color=axis_color, width=1))

            self.perform_fit() # Re-plot with new settings
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to apply plot settings: {e}\n{traceback.format_exc()}")

    def prepare_data(self, x_key, y_key, z_key, index_key): # Added from 5th
        x = self.data.get(x_key, np.array([])) # Use .get for safety
        y = self.data.get(y_key, np.array([]))
        
        z = None
        if z_key and z_key != "None" and z_key in self.data:
            z = self.data[z_key]
        
        index = self.data.get(index_key) if index_key and index_key != "None" and index_key in self.data else None
        
        # Ensure x and y are numpy arrays for consistent processing
        x = np.asarray(x)
        y = np.asarray(y)

        min_len = min(len(x), len(y)) # This might be problematic if one is much shorter
        # It's better to ensure data was aligned during import or handle here
        # For now, assume they are of compatible lengths from import
        if len(x) != len(y) and min_len > 0 : # Basic attempt to align if lengths differ post-selection
             x = x[:min_len]
             y = y[:min_len]
             if z is not None and len(z) > min_len: z = z[:min_len]
             if index is not None and len(index) > min_len: index = index[:min_len]
        elif len(x) == 0 or len(y) == 0: # If either is empty, return empty arrays
            return np.array([]), np.array([]), None, None

        return x, y, z, index

    def perform_fit(self):
        if not self.data:
            return
        try:
            x_key = self.x_data_combo.currentText()
            y_key = self.y_data_combo.currentText()
            if not self.validate_data(x_key, y_key):
                return
            z_key = self.z_data_combo.currentText()
            if z_key == "None":
                z_key = None
            index_key = self.index_data_combo.currentText()
            if index_key == "None":
                index_key = None
            x, y, z, index = self.prepare_data(x_key, y_key, z_key, index_key)
            
            if len(x) == 0 or len(y) == 0: # Check after prepare_data
                self.plot_widget.clear() # Clear plot if no data
                return
            # Create x values for the plot curve
            x_plot = np.linspace(np.min(x), np.max(x), 1000)
            fit_type = self.fit_type_combo.currentText()
            exponential_version = None
            power_version = None
            if fit_type == "Exponential":
                exponential_version = self.exponential_version_combo.currentIndex()
            elif fit_type == "Power":
                power_version = self.power_version_combo.currentIndex()
            result = self.fit_model(fit_type, x, y, x_plot, exponential_version, power_version)
            if result and result[0] is not None: # Check if fitted_y (first element) is not None
                fitted_y, y_plot, equation, params, y_lower, y_upper = result
                self.update_result_labels(equation, params, y, fitted_y)
                self.update_plot(x, y, z, index, x_plot, y_plot, y_lower, y_upper)
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Fitting error: {str(e)}\n{traceback.format_exc()}")

    def validate_data(self, x_key, y_key):
        if x_key not in self.data or y_key not in self.data:
            QMessageBox.critical(self, "Error", "Selected X or Y data not found.")
            return False
        if len(self.data[x_key]) == 0 or len(self.data[y_key]) == 0:
            QMessageBox.information(self, "Info", "Selected X or Y data is empty.")
            self.plot_widget.clear() # Clear plot if data is empty
            return False
        return True

    def fit_model(self, fit_type, x, y, x_plot, exponential_version=None, power_version=None): # Added based on 5th and 6th needs
        try:
            if fit_type == "Polynomial":
                return self.fit_polynomial(x, y, x_plot)
            elif fit_type == "Custom":
                return self.fit_custom_function(x, y, x_plot)
            elif fit_type == "Pre-defined Custom":
                return self.fit_predefined_custom(x, y, x_plot)
            elif fit_type == "Exponential":
                return self.fit_standard_function(fit_type, x, y, x_plot, exponential_version=exponential_version)
            elif fit_type == "Power":
                return self.fit_standard_function(fit_type, x, y, x_plot, power_version=power_version)
            else: # Linear
                return self.fit_standard_function(fit_type, x, y, x_plot)
        except Exception as e:
            QMessageBox.critical(self, "Fitting Error", f"Error during model fitting: {str(e)}\n{traceback.format_exc()}")
            return None, None, None, None, None, None # Ensure 6 items are returned for unpacking

    def calculate_uncertainty_bounds(self, y, fitted_y, x_plot, y_plot, n_params): # Added from 5th
        """Calculate uncertainty bounds with improved robustness"""
        try:
            residuals = y - fitted_y
            n = len(y)
            if n <= n_params or n_params == 0: # also check n_params to avoid division by zero if no params
                return y_plot, y_plot 
            
            mse = np.sum(residuals**2) / (n - n_params)
            ser = np.sqrt(mse)
            
            t_value = 2.0  # Approximate 95% confidence interval (can use scipy.stats.t.ppf(0.975, n - n_params) for more accuracy)
            # For simplicity, using fixed t_value. For prediction bands, variance of prediction is more complex.
            # This is a simplified confidence band for the mean response, or approx prediction band.
            y_lower = y_plot - t_value * ser 
            y_upper = y_plot + t_value * ser
            return y_lower, y_upper
        except Exception as e:
            print(f"Warning: Error calculating uncertainty bounds: {str(e)}")
        return True

    def fit_polynomial(self, x, y, x_plot):
        """Fit polynomial function to data"""
        try:
            degree = int(self.degree_combo.currentText())

            # Fit polynomial using numpy polyfit
            coeffs = np.polyfit(x, y, degree)
            poly_func = np.poly1d(coeffs)

            fitted_y = poly_func(x)
            y_plot = poly_func(x_plot)

            # Calculate uncertainty bounds
            y_lower, y_upper = self.calculate_uncertainty_bounds(y, fitted_y, x_plot, y_plot, len(coeffs))

            # Create equation string
            equation_parts = []
            for i, coeff in enumerate(coeffs):
                power = degree - i
                if power == 0:
                    equation_parts.append(f"{coeff:.4f}")
                elif power == 1:
                    equation_parts.append(f"{coeff:.4f}x")
                else:
                    equation_parts.append(f"{coeff:.4f}x^{power}")

            equation = f"Polynomial (degree {degree}): f(x) = " + " + ".join(equation_parts)
            equation = equation.replace("+ -", "- ")  # Fix negative signs

            # Create parameter list
            params = [f"a{degree-i} = {coeff:.4f}" for i, coeff in enumerate(coeffs)]

            return fitted_y, y_plot, equation, params, y_lower, y_upper

        except Exception as e:
            raise ValueError(f"Error in polynomial fit: {str(e)}")

    def fit_custom_function(self, x, y, x_plot):
        """Fit custom user-defined function to data"""
        try:
            func_str = self.custom_func_edit.text().strip()
            if not func_str:
                raise ValueError("Custom function is empty")

            # Parse the function string to extract parameters
            # Simple approach: assume parameters are single letters (a, b, c, etc.)
            import re
            param_pattern = r'\b[a-z]\b'
            params_found = list(set(re.findall(param_pattern, func_str)))
            params_found.sort()  # Sort alphabetically

            if not params_found:
                raise ValueError("No parameters found in custom function")

            # Create a lambda function from the string
            x_sym = symbols('x')
            param_symbols = symbols(params_found)

            try:
                expr = sympify(func_str)
                func = lambdify([x_sym] + list(param_symbols), expr, 'numpy')
            except Exception as e:
                raise ValueError(f"Invalid function syntax: {str(e)}")

            # Define the model function for curve_fit
            def model(x_vals, *params):
                return func(x_vals, *params)

            # Initial guess (all parameters = 1.0)
            p0 = [1.0] * len(params_found)

            # Get fitting parameters from UI
            max_iterations = self.max_iter_spin.value()
            method = self.fit_method_combo.currentText()

            # Fit the function
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                popt, pcov = curve_fit(model, x, y, p0=p0, method=method, maxfev=max_iterations)

            fitted_y = model(x, *popt)
            y_plot = model(x_plot, *popt)

            # Calculate uncertainty bounds
            y_lower, y_upper = self.calculate_uncertainty_bounds(y, fitted_y, x_plot, y_plot, len(popt))

            # Create equation string
            equation = f"Custom: f(x) = {func_str}"
            for param, value in zip(params_found, popt):
                equation = equation.replace(param, f"{value:.4f}")

            # Create parameter list
            params = [f"{param} = {value:.4f}" for param, value in zip(params_found, popt)]

            return fitted_y, y_plot, equation, params, y_lower, y_upper

        except Exception as e:
            raise ValueError(f"Error in custom function fit: {str(e)}")

    def fit_predefined_custom(self, x, y, x_plot):
        """Fit predefined custom function to data"""
        try:
            func_name = self.predefined_func_combo.currentText()
            if func_name not in self.custom_functions:
                raise ValueError(f"Unknown predefined function: {func_name}")

            func_info = self.custom_functions[func_name]
            model = func_info["model"]
            equation_template = func_info["equation"]
            param_names = func_info["params"]

            # Get fitting parameters from UI
            max_iterations = self.max_iter_spin.value()
            method = self.fit_method_combo.currentText()
            use_smart_guess = self.use_smart_guess_check.isChecked()
            use_bounds = self.use_bounds_check.isChecked()

            # Initial guess
            if use_smart_guess and "initial_guess" in func_info:
                try:
                    p0 = func_info["initial_guess"](x, y)
                except:
                    p0 = [1.0] * len(param_names)
            else:
                p0 = [1.0] * len(param_names)

            # Bounds
            bounds = (-np.inf, np.inf)
            if use_bounds and "bounds" in func_info:
                bounds = func_info["bounds"]

            # Fit the function
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                if use_bounds and bounds != (-np.inf, np.inf):
                    popt, pcov = curve_fit(model, x, y, p0=p0, bounds=bounds,
                                         method=method, maxfev=max_iterations)
                else:
                    popt, pcov = curve_fit(model, x, y, p0=p0, method=method,
                                         maxfev=max_iterations)

            fitted_y = model(x, *popt)
            y_plot = model(x_plot, *popt)

            # Calculate uncertainty bounds
            y_lower, y_upper = self.calculate_uncertainty_bounds(y, fitted_y, x_plot, y_plot, len(popt))

            # Create equation string
            equation = equation_template.format(**{name: f"{val:.4f}" for name, val in zip(param_names, popt)})

            # Create parameter list
            params = [f"{name} = {val:.4f}" for name, val in zip(param_names, popt)]

            return fitted_y, y_plot, equation, params, y_lower, y_upper

        except Exception as e:
            raise ValueError(f"Error in predefined custom function fit: {str(e)}")

    def fit_standard_function(self, fit_type, x, y, x_plot, exponential_version=None, power_version=None):
        try:
            # Get fitting parameters from UI
            max_iterations = self.max_iter_spin.value()
            method = self.fit_method_combo.currentText()
            use_smart_guess = self.use_smart_guess_check.isChecked()
            use_bounds = self.use_bounds_check.isChecked()

            # Default parameters
            p0 = None  # Default initial guess
            bounds = (-np.inf, np.inf)  # Default bounds (no constraints)

            if fit_type == "Linear":
                def model(x, a, b): return a * x + b
                equation_template = "Linear: f(x) = {a} x + {b}"
                param_names = ["a", "b"]

                if use_smart_guess:
                    # Estimate slope and intercept using first and last point
                    if len(x) >= 2:
                        idx_first, idx_last = 0, -1
                        slope = (y[idx_last] - y[idx_first]) / (x[idx_last] - x[idx_first])
                        intercept = y[idx_first] - slope * x[idx_first]
                        p0 = [slope, intercept]
                    else:
                        p0 = [1.0, 0.0]

                if use_bounds:
                    # No real need for bounds for linear fits, but we could restrict to reasonable ranges
                    bounds = ([-np.inf, -np.inf], [np.inf, np.inf])

            elif fit_type == "Power":
                if power_version is None:
                    power_version = 0

                if power_version == 0:  # Standard: a * x^b
                    def model(x, a, b): return a * np.power(x, b)
                    equation_template = "Power: f(x) = {a} x^{b}"
                    param_names = ["a", "b"]

                    if use_smart_guess:
                        # For power law (y = a*x^b), use log transformation to estimate parameters
                        # Log(y) = log(a) + b*log(x)
                        valid_mask = (x > 0) & (y > 0)  # Need positive values for log transform
                        if np.sum(valid_mask) >= 2:
                            log_x = np.log(x[valid_mask])
                            log_y = np.log(y[valid_mask])
                            coeffs = np.polyfit(log_x, log_y, 1)
                            b_init = coeffs[0]  # Slope
                            log_a = coeffs[1]  # Intercept
                            a_init = np.exp(log_a)
                            p0 = [a_init, b_init]
                        else:
                            p0 = [1.0, 1.0]
                    else:
                        p0 = [1.0, 1.0]

                    if use_bounds:
                        # For power law, typically a > 0 (scaling factor)
                        # b can be any real number, but often we expect specific ranges
                        bounds = ([1e-10, -10], [np.inf, 10])

                elif power_version == 1:  # Power with Asymptote: c + k * x^a
                    def model(x, c, k, a): return c + k * np.power(x, a)
                    equation_template = "Power with Asymptote: f(x) = {c} + {k} x^{a}"
                    param_names = ["c", "k", "a"]

                    if use_smart_guess:
                        # Strategy: Estimate asymptote c, then use transformation for k and a
                        try:
                            # Sort data by x
                            sort_idx = np.argsort(x)
                            x_sorted = x[sort_idx]
                            y_sorted = y[sort_idx]

                            # Is data generally increasing or decreasing?
                            increasing = y_sorted[-1] > y_sorted[0]

                            if increasing:
                                # For increasing data, asymptote is at the larger end
                                # Use the last few points to estimate c
                                last_n = min(3, len(y_sorted) // 4)  # Use last 3 points or 1/4 of data
                                if last_n > 0:
                                    c_init = np.mean(y_sorted[-last_n:])  # Average of last few points
                                else:
                                    c_init = y_sorted[-1]  # Just use the last point

                                # Compute transformed data: (y - c) = k * x^a
                                # log(y - c) = log(k) + a * log(x)
                                valid_mask = (x_sorted > 0) & ((y_sorted - c_init) > 0)
                                if np.sum(valid_mask) >= 2:
                                    log_x = np.log(x_sorted[valid_mask])
                                    log_y_trans = np.log(y_sorted[valid_mask] - c_init)

                                    # Linear regression on transformed data
                                    coeffs = np.polyfit(log_x, log_y_trans, 1)
                                    a_init = coeffs[0]  # Slope
                                    log_k = coeffs[1]  # Intercept
                                    k_init = np.exp(log_k)
                                else:
                                    # Not enough valid data points after transformation
                                    a_init = 0.5  # Power less than 1 for diminishing returns
                                    k_init = (y_sorted[-1] - c_init) / (x_sorted[-1] ** a_init)
                            else:
                                # For decreasing data, asymptote is usually at the smaller end
                                # Use the first few points to estimate c
                                last_n = min(3, len(y_sorted) // 4)  # Use first 3 points or 1/4 of data
                                if last_n > 0:
                                    c_init = np.mean(y_sorted[:last_n])  # Average of first few points
                                else:
                                    c_init = y_sorted[0]  # Just use the first point

                                # For decreasing trend, k is likely negative
                                # (y - c) = k * x^a where k < 0
                                # We can still use log transform but on absolute values
                                valid_mask = (x_sorted > 0) & ((c_init - y_sorted) > 0)
                                if np.sum(valid_mask) >= 2:
                                    log_x = np.log(x_sorted[valid_mask])
                                    log_y_trans = np.log(c_init - y_sorted[valid_mask])

                                    # Linear regression on transformed data
                                    coeffs = np.polyfit(log_x, log_y_trans, 1)
                                    a_init = coeffs[0]  # Slope
                                    log_k = coeffs[1]  # Intercept
                                    k_init = -np.exp(log_k)  # Negative k for decreasing trend
                                else:
                                    # Not enough valid data points after transformation
                                    a_init = 0.5  # Power less than 1 for diminishing returns
                                    k_init = (y_sorted[0] - c_init) / (x_sorted[0] ** a_init)

                            p0 = [c_init, k_init, a_init]
                        except:
                            # Fallback if estimation fails
                            p0 = [np.min(y), 1.0, 0.5]
                    else:
                        # Default initial guesses
                        p0 = [np.min(y), 1.0, 0.5]

                    if use_bounds:
                        # For power with asymptote:
                        # c can be any real number
                        # k can be positive or negative
                        # a is typically positive, but can be negative for 1/x type behavior
                        bounds = ([-np.inf, -np.inf, -5], [np.inf, np.inf, 10])
                else:
                    raise ValueError("Unknown power function version selected.")

            elif fit_type == "Exponential":
                if len(x) == 0 or len(y) == 0:
                    QMessageBox.warning(self, "Fitting Error", "Cannot perform exponential fit with empty X or Y data.")
                    return None, None, "Fit failed: No data", [], None, None

                if exponential_version is None:
                    exponential_version = 0

                if exponential_version == 0:  # Standard: a * exp(bx)
                    def model(x, a, b): return a * np.exp(b * x)
                    equation_template = "Exponential: f(x) = {a} e^({b} x)"
                    param_names = ["a", "b"]

                    if use_smart_guess:
                        # For y = a*exp(b*x), use log transform
                        # ln(y) = ln(a) + b*x
                        # Robust p0 for a * exp(bx)
                        y_pos = y[y > 0]
                        x_pos = x[y > 0]
                        if len(y_pos) >= 2 and len(x_pos) >= 2:
                            try:
                                log_y_vals = np.log(y_pos)
                                coeffs = np.polyfit(x_pos, log_y_vals, 1)
                                b_init = coeffs[0]
                                a_init = np.exp(coeffs[1])
                                p0 = [a_init, b_init]
                            except Exception: # Catch any error during polyfit/log
                                p0 = [np.mean(y) if len(y) > 0 and np.mean(y) > 1e-9 else 1.0, 0.1]
                        else: # Not enough positive data
                            p0 = [np.mean(y) if len(y) > 0 and np.mean(y) > 1e-9 else 1.0, 0.1]
                    else:
                        p0 = [np.mean(y) if len(y) > 0 and np.mean(y) > 1e-9 else 1.0, 0.1]

                    if use_bounds:
                        # For standard exponential, typically a > 0 (scaling factor)
                        # b can be positive (growing) or negative (decaying)
                        bounds = ([1e-9, -np.inf], [np.inf, np.inf]) # Loosen b bounds, ensure a is positive

                elif exponential_version == 1:  # Generalized: A * exp(kx)
                    def model(x, A, k): return A * np.exp(k * x)
                    equation_template = "Generalized Exponential: f(x) = {A} exp({k} x)"
                    param_names = ["A", "k"]

                    if use_smart_guess:
                        # Same as standard exponential
                        # Robust p0 for A * exp(kx) - similar to version 0
                        y_pos = y[y > 0]
                        x_pos = x[y > 0]
                        if len(y_pos) >= 2 and len(x_pos) >= 2:
                            try:
                                log_y_vals = np.log(y_pos)
                                coeffs = np.polyfit(x_pos, log_y_vals, 1)
                                k_init = coeffs[0]
                                A_init = np.exp(coeffs[1])
                                p0 = [A_init, k_init]
                            except Exception:
                                p0 = [np.mean(y) if len(y) > 0 and np.mean(y) > 1e-9 else 1.0, 0.1]
                        else: # Not enough positive data
                            p0 = [np.mean(y) if len(y) > 0 and np.mean(y) > 1e-9 else 1.0, 0.1]
                    else:
                        p0 = [np.mean(y) if len(y) > 0 and np.mean(y) > 1e-9 else 1.0, 0.1]

                    if use_bounds:
                        # Similar to standard exponential
                        bounds = ([1e-9, -np.inf], [np.inf, np.inf]) # Loosen k bounds, ensure A is positive

                elif exponential_version == 2:  # Base b: A * b^x
                    def model(x, A, b): return A * b ** x
                    equation_template = "Exponential Base b: f(x) = {A} {b}^x"
                    param_names = ["A", "b"]

                    if use_smart_guess:
                        # For y = A*b^x, use log transform
                        # log(y) = log(A) + x*log(b)
                        # Robust p0 for A * b^x
                        y_pos = y[y > 0]
                        x_pos = x[y > 0]
                        if len(y_pos) >= 2 and len(x_pos) >= 2:
                            try:
                                log_y_vals = np.log(y_pos)
                                coeffs = np.polyfit(x_pos, log_y_vals, 1) # log(y) = log(A) + x*log(b)
                                log_b_val = coeffs[0]
                                log_A_val = coeffs[1]
                                b_init = np.exp(log_b_val)
                                A_init = np.exp(log_A_val)
                                p0 = [A_init, b_init]
                            except Exception:
                                p0 = [np.mean(y) if len(y) > 0 and np.mean(y) > 1e-9 else 1.0, 1.1]
                        else: # Not enough positive data
                            p0 = [np.mean(y) if len(y) > 0 and np.mean(y) > 1e-9 else 1.0, 1.1]
                    else:
                        p0 = [np.mean(y) if len(y) > 0 and np.mean(y) > 1e-9 else 1.0, 1.1]

                    if use_bounds:
                        # For base b exponential, both A and b are typically positive
                        bounds = ([1e-9, 1e-9], [np.inf, np.inf]) # Ensure A and b are positive

                elif exponential_version == 3:  # Decay with Asymptote: A * exp(-kx) + C
                    def model(x, A, k, C): return A * np.exp(-k * x) + C
                    equation_template = "Exponential Decay with Asymptote: f(x) = {A} e^(-{k} x) + {C}"
                    param_names = ["A", "k", "C"]

                    if use_smart_guess:
                        # For y = A*exp(-k*x) + C, estimate:
                        # C ≈ asymptotic value (value at largest x)
                        # A ≈ y_initial - C (distance from initial to asymptote)
                        # k ≈ rate based on how quickly the curve approaches C
                        if len(x) >= 3 and len(y) >=3:
                            try:
                                sorted_indices = np.argsort(x)
                                x_sorted = x[sorted_indices]
                                y_sorted = y[sorted_indices]
                                if not len(y_sorted): raise ValueError("Empty y_sorted")


                                # Asymptote C: approx. value at largest x
                                # Take average of last few points to reduce noise
                                last_n = min(5, len(y_sorted) // 3)  # Use up to 5 points or 1/3 of data
                                C_init = np.mean(y_sorted[-last_n:]) if last_n > 0 and len(y_sorted) > 0 else (y_sorted[-1] if len(y_sorted) > 0 else 0.0)

                                # Amplitude A: approx. difference from start to asymptote
                                A_init = (y_sorted[0] - C_init) if len(y_sorted) > 0 else 1.0

                                # Rate k: estimate using half-life approach
                                # Find x where y has decayed to about 37% (1/e) of initial value above asymptote
                                k_init = 0.1 # Default k
                                if A_init != 0 and len(y_sorted) > 0: # Avoid division by zero for target_y
                                    target_y = C_init + A_init / np.e
                                    if len(y_sorted) > 0:
                                       closest_idx = np.argmin(np.abs(y_sorted - target_y))
                                       if closest_idx > 0 and closest_idx < len(x_sorted): # Ensure index is valid
                                           half_life_x = x_sorted[closest_idx]
                                           if half_life_x != 0: # Avoid division by zero for k_init
                                               k_init = 1.0 / half_life_x

                                p0 = [A_init, k_init, C_init]
                            except Exception:
                                y_min_val = np.min(y) if len(y) > 0 else 0.0
                                y_max_val = np.max(y) if len(y) > 0 else 1.0
                                y_range = y_max_val - y_min_val if len(y) > 0 else 1.0
                                p0 = [y_range if y_range > 0 else 1.0, 0.1, y_min_val]
                        else: # Not enough data
                            y_min_val = np.min(y) if len(y) > 0 else 0.0
                            y_max_val = np.max(y) if len(y) > 0 else 1.0
                            y_range = y_max_val - y_min_val if len(y) > 0 else 1.0
                            p0 = [y_range if y_range > 0 else 1.0, 0.1, y_min_val]
                    else:
                        y_min_val = np.min(y) if len(y) > 0 else 0.0
                        y_max_val = np.max(y) if len(y) > 0 else 1.0
                        y_range = y_max_val - y_min_val if len(y) > 0 else 1.0
                        p0 = [y_range if y_range > 0 else 1.0, 0.1, y_min_val]

                    if use_bounds:
                        # For decay with asymptote:
                        # A can be positive or negative depending on if it's decay or growth to asymptote
                        # k should be positive for decay (negative in the exponent already)
                        # C can be any real number (the asymptote)
                        bounds = ([-np.inf, 1e-9, -np.inf], [np.inf, np.inf, np.inf]) # k must be positive

                elif exponential_version == 4:  # Two-Term Exponential: A*e^(f*x) + B*e^(g*x)
                    def model(x, A, f, B, g): return A * np.exp(f * x) + B * np.exp(g * x)
                    equation_template = "Two-Term Exponential: f(x) = {A} e^({f} x) + {B} e^({g} x)"
                    param_names = ["A", "f", "B", "g"]

                    if use_smart_guess:
                        # For two-term exponential, initial guessing is challenging
                        # We'll use a heuristic approach trying to separate fast and slow components
                        if len(x) >= 5 and len(y) >= 5:
                            try:
                                sort_idx = np.argsort(x)
                                x_sorted = x[sort_idx]
                                y_sorted = y[sort_idx]
                                if not len(y_sorted): raise ValueError("Empty y_sorted")

                                # Estimate if data is generally increasing or decreasing
                                increasing = (y_sorted[-1] > y_sorted[0]) if len(y_sorted) > 1 else True

                                if increasing:
                                    # For increasing data, assume positive rates
                                    # First term dominates early behavior
                                    A_init = y_sorted[0] * 0.7  # 70% of initial value
                                    f_init = 0.05  # Slower rate

                                    # Second term captures later behavior
                                    B_init = y_sorted[-1] - A_init * np.exp(f_init * x_sorted[-1])  # Remaining value
                                    g_init = 0.2  # Faster rate
                                else:
                                    # For decreasing data, assume negative rates for decay
                                    # Estimate two components - fast and slow decay

                                    # First term: fast decay component
                                    A_init = y_sorted[0] * 0.6  # 60% of initial is fast component
                                    f_init = -0.2  # Fast decay rate (negative)

                                    # Second term: slow decay component
                                    B_init = y_sorted[0] - A_init  # Remaining of initial value
                                    g_init = -0.05  # Slow decay rate (negative)

                                p0 = [A_init, f_init, B_init, g_init]
                            except Exception: # Catch any error during smart guess for exp version 4
                                y_min_val = np.min(y) if len(y) > 0 else 0.0
                                y_max_val = np.max(y) if len(y) > 0 else 1.0
                                y_range = y_max_val - y_min_val if len(y) > 0 else 1.0
                                p0 = [y_range/2 if y_range > 0 else 0.5, 0.1, y_range/2 if y_range > 0 else 0.5, -0.1]
                        else: # Not enough data
                            y_min_val = np.min(y) if len(y) > 0 else 0.0
                            y_max_val = np.max(y) if len(y) > 0 else 1.0
                            y_range = y_max_val - y_min_val if len(y) > 0 else 1.0
                            p0 = [y_range/2 if y_range > 0 else 0.5, 0.1, y_range/2 if y_range > 0 else 0.5, -0.1]
                    else:
                        y_min_val = np.min(y) if len(y) > 0 else 0.0
                        y_max_val = np.max(y) if len(y) > 0 else 1.0
                        y_range = y_max_val - y_min_val if len(y) > 0 else 1.0
                        p0 = [y_range/2 if y_range > 0 else 0.5, 0.1, y_range/2 if y_range > 0 else 0.5, -0.1]

                    if use_bounds:
                        # For two-term exponential, we need to ensure the rates are different
                        # But we don't want to constrain them too much as they could be positive or negative
                        # No specific bounds on amplitudes A and B
                        bounds = ([-np.inf, -np.inf, -np.inf, -np.inf], 
                                  [np.inf, np.inf, np.inf, np.inf])
                else:
                    raise ValueError("Unknown exponential version selected.")
            else:
                raise ValueError(f"Unknown fit type: {fit_type}")

            # --- p0 Validation and Clamping (Common for all standard fits before curve_fit) ---
            # Ensure p0, param_names, and bounds are defined if we reached here.
            # (Error conditions for unknown fit_type/exp_version should have returned already)

            if 'p0' not in locals() or p0 is None: # p0 should always be defined by this point
                if 'param_names' in locals() and param_names:
                    QMessageBox.warning(self, "Fitting Warning", f"p0 was not defined for {fit_type}. Using safe defaults.")
                    p0 = [1.0] * len(param_names)
                else: # Absolute fallback, should ideally not be reached
                    QMessageBox.critical(self, "Fitting Error", f"Critical: p0 and param_names not defined for {fit_type}.")
                    return None, None, "Fit failed: Internal p0/param_names error", [], None, None

            if 'param_names' not in locals() or not param_names: # Should be defined
                 QMessageBox.critical(self, "Fitting Error", f"Internal error: param_names not set before clamping for {fit_type}.")
                 return None, None, "Fit failed: Internal param_names error", [], None, None

            if isinstance(p0, (list, np.ndarray)) and (np.any(np.isnan(p0)) or np.any(np.isinf(p0))):
                QMessageBox.warning(self, "Fitting Warning", f"Initial guess p0 for {fit_type} contains NaN/inf: {p0}. Attempting to use safe defaults.")
                p0 = [1.0] * len(param_names) # Reset to default if NaN/inf

            # Clamp p0 to bounds if use_bounds is True
            if use_bounds and 'bounds' in locals() and bounds != (-np.inf, np.inf) and isinstance(bounds, tuple) and len(bounds) == 2:
                if len(p0) == len(param_names) and isinstance(bounds[0], (list, np.ndarray)) and len(bounds[0]) == len(param_names) and isinstance(bounds[1], (list, np.ndarray)) and len(bounds[1]) == len(param_names):
                    clamped_p0 = []
                    for i_param, val_param in enumerate(p0):
                        low_bound = bounds[0][i_param]
                        high_bound = bounds[1][i_param]
                        
                        current_val = val_param
                        # Attempt to reset problematic current_val before clamping
                        if not np.isfinite(current_val):
                            QMessageBox.warning(self, "Fitting Warning", f"Parameter {param_names[i_param]} initial guess is {current_val}. Resetting for clamping.")
                            if np.isfinite(low_bound) and np.isfinite(high_bound):
                                current_val = (low_bound + high_bound) / 2.0
                            elif np.isfinite(low_bound):
                                current_val = low_bound + 0.1 * abs(low_bound) if low_bound != 0 else 0.1
                            elif np.isfinite(high_bound):
                                current_val = high_bound - 0.1 * abs(high_bound) if high_bound != 0 else -0.1
                            else: # Both bounds are inf
                                current_val = 1.0
                        
                        actual_low = low_bound if np.isfinite(low_bound) else -np.inf
                        actual_high = high_bound if np.isfinite(high_bound) else np.inf
                        
                        clamped_val = current_val
                        if np.isfinite(current_val):
                             clamped_val = max(actual_low, min(current_val, actual_high))
                        
                        clamped_p0.append(clamped_val)
                    
                    if np.any(np.isnan(clamped_p0)) or np.any(np.isinf(clamped_p0)):
                        QMessageBox.warning(self, "Fitting Warning", f"Clamped p0 still contains NaN/inf for {fit_type}. Fit may fail. Original p0: {p0}, Clamped p0: {clamped_p0}")
                    else:
                        p0 = clamped_p0 # Assign clamped_p0 only if it's clean
                else:
                    QMessageBox.warning(self, "Fitting Warning",
                                        f"p0 (len {len(p0)}), param_names (len {len(param_names)}), or bounds "
                                        f"(len {len(bounds[0]) if bounds and len(bounds)>0 and isinstance(bounds[0], (list, np.ndarray)) else 'N/A'}, "
                                        f"{len(bounds[1]) if bounds and len(bounds)>1 and isinstance(bounds[1], (list, np.ndarray)) else 'N/A'}) "
                                        f"dimensions mismatch for {fit_type}. Skipping clamping.")
            # --- End of p0 Validation and Clamping ---
            if p0 is None or np.any(np.isnan(p0)) or np.any(np.isinf(p0)):
                # Fallback to a generic safe p0 if something went wrong
                QMessageBox.warning(self, "Fitting Warning", "Initial guess calculation failed or resulted in NaN/inf. Using safe defaults.")
                p0 = [1.0] * len(param_names) # Generic default

            # Clamp p0 to bounds if use_bounds is True
            if use_bounds and bounds != (-np.inf, np.inf) and isinstance(bounds, tuple) and len(bounds) == 2:
                if len(p0) == len(bounds[0]) and len(p0) == len(bounds[1]): # Check consistency
                    clamped_p0 = []
                    for i, val in enumerate(p0):
                        low = bounds[0][i]
                        high = bounds[1][i]
                        # Ensure low and high are not NaN/inf if val is finite
                        if np.isfinite(val):
                            if not np.isfinite(low) and val < 0 : low = -np.inf # allow negative if val is neg and low is -inf
                            elif not np.isfinite(low): low = 0 # default low to 0 if val is pos
                            if not np.isfinite(high) and val > 0: high = np.inf # allow positive if val is pos and high is inf
                            elif not np.isfinite(high): high = 0 # default high to 0 if val is neg
                        
                        clamped_val = max(low, min(val, high))
                        clamped_p0.append(clamped_val)
                    p0 = clamped_p0
                else:
                    QMessageBox.warning(self, "Fitting Warning", "p0 and bounds dimensions mismatch. Skipping clamping.")

            # Fallback to default initial guess if not set or smart guess fails
            if p0 is None:
                p0 = [1.0] * len(param_names)

            # Only use method parameter with compatible bounds
            if method == "lm" and use_bounds and bounds != (-np.inf, np.inf):
                # lm method doesn't support bounds, so fall back to trf
                actual_method = "trf"
            else:
                actual_method = method

            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                if use_bounds and bounds != (-np.inf, np.inf):
                    popt, pcov = curve_fit(model, x, y, p0=p0, bounds=bounds, 
                                           method=actual_method, maxfev=max_iterations)
                else:
                    popt, pcov = curve_fit(model, x, y, p0=p0, method=actual_method, 
                                           maxfev=max_iterations)

            fitted_y = model(x, *popt)
            y_plot = model(x_plot, *popt)
            y_lower, y_upper = self.calculate_uncertainty_bounds(y, fitted_y, x_plot, y_plot, len(popt))

            equation = equation_template.format(**{name: f"{val:.4f}" for name, val in zip(param_names, popt)})
            params = [f"{name} = {val:.4f}" for name, val in zip(param_names, popt)]

            return fitted_y, y_plot, equation, params, y_lower, y_upper
        except Exception as e:
            raise ValueError(f"Error in standard function fit: {str(e)}")


    def update_result_labels(self, equation, params, y, fitted_y):
        if params is None or fitted_y is None: # Check if fitting failed
            self.equation_label.setText(equation if equation else "Equation: Fit failed or not applicable")
            for i in range(len(self.param_labels)):
                self.param_labels[i].setText("") # Clear param labels
            self.sse_label.setText("SSE: N/A")
            self.rsquare_label.setText("R-square: N/A")
            self.rmse_label.setText("RMSE: N/A")
            self.ser_label.setText("SER: N/A") # Added SER clearing
            return

        self.equation_label.setText(equation)
        for i, param_text in enumerate(params): # Renamed param to param_text to avoid conflict
            if i < len(self.param_labels):
                self.param_labels[i].setText(param_text)
        for i in range(len(params), len(self.param_labels)): # Clear remaining labels
            self.param_labels[i].setText("")

        # Ensure y and fitted_y are numpy arrays and have the same length for calculations
        y_arr = np.asarray(y)
        fitted_y_arr = np.asarray(fitted_y)

        if len(y_arr) == 0 or len(fitted_y_arr) == 0 or len(y_arr) != len(fitted_y_arr):
            QMessageBox.warning(self, "Result Warning", "Mismatch in y and fitted_y data for metrics calculation.")
            self.sse_label.setText("SSE: Error")
            self.rsquare_label.setText("R-square: Error")
            self.rmse_label.setText("RMSE: Error")
            self.ser_label.setText("SER: Error")
            return

        residuals = y_arr - fitted_y_arr
        sse = np.sum(residuals**2)
        sst = np.sum((y_arr - np.mean(y_arr))**2)
        rsquare = 1 - (sse / sst) if sst != 0 else 0.0 # Ensure float
        rmse = np.sqrt(sse / len(y_arr)) if len(y_arr) > 0 else 0.0
        n = len(y_arr)
        p_count = len(params) if params else 0 # Renamed p to p_count
        ser = np.sqrt(sse / (n - p_count)) if n > p_count and (n - p_count) > 0 else 0.0 # Check n-p > 0
        
        self.sse_label.setText(f"SSE: {sse:.4g}") # Use .4g for better formatting
        self.rsquare_label.setText(f"R-square: {rsquare:.4f}")
        self.rmse_label.setText(f"RMSE: {rmse:.4g}")
        self.ser_label.setText(f"SER: {ser:.4f}")

    def update_plot(self, x, y, z, index, x_plot, y_plot, y_lower, y_upper):
        self.plot_widget.clear()
        point_size = float(self.point_size_edit.text())
        # Get the selected point shape
        shape_name = self.point_shape_combo.currentText().lower()
        symbol = self.get_symbol_for_shape(shape_name)
        # Create scatter plot
        if z is not None and len(z) > 0:  # Ensure z data exists and is not empty
            scatter_item = self.create_colored_scatter(x, y, z, index, point_size, symbol)
        else:
            # Use the selected single color
            color_name = self.single_color_combo.currentText().lower().replace(" ", "")
            color = self.get_color_from_name(color_name)

            scatter_item = pg.ScatterPlotItem(
                x=x, y=y, size=point_size, symbol=symbol, pen=pg.mkPen(None), 
                brush=pg.mkBrush(color), hoverable=True,
                tip=lambda x, y, data, i: f"x: {x:.2f}\ny: {y:.2f}\nindex: {index[int(i)] if index is not None else int(i)}"
            )

        self.plot_widget.addItem(scatter_item)

        # Add text labels if index data is provided and checkbox is checked
        if index is not None and len(index) > 0 and self.show_labels_check.isChecked():
            # Calculate label positions to avoid overlap
            label_positions = self.calculate_label_positions(x, y, index)
            for (xi, yi), label in zip(label_positions, index):
                text_item = pg.TextItem(
                    text=str(label), 
                    anchor=(0.5, -0.5),  # Position above point
                    color=(0, 0, 0),     # Black text
                    fill=pg.mkBrush(255, 255, 255, 100)  # Semi-transparent white background
                )
                text_item.setPos(xi, yi)
                font = text_item.textItem.font()
                font.setPointSize(8)  # Adjust as needed
                text_item.setFont(font)
                self.plot_widget.addItem(text_item)

        # Plot the fitted curve
        if y_plot is not None:
            fit_curve = pg.PlotDataItem(x_plot, y_plot, pen=pg.mkPen('r', width=2), name='Fit')
            self.plot_widget.addItem(fit_curve)

            if self.show_uncertainty_check.isChecked() and y_lower is not None and y_upper is not None:
                uncertainty_fill = pg.FillBetweenItem(
                    pg.PlotDataItem(x_plot, y_lower, pen=pg.mkPen('r', width=0)),
                    pg.PlotDataItem(x_plot, y_upper, pen=pg.mkPen('r', width=0)),
                    brush=pg.mkBrush((255, 0, 0, 50)))
                self.plot_widget.addItem(uncertainty_fill)

            # Add equation as text in the plot if requested
            if self.show_equation_legend_check.isChecked():
                # Get the current equation from the equation label
                equation_text = self.equation_label.text()
                if equation_text:
                    # Extract just the equation part, removing the fit type prefix
                    if ": f(x) = " in equation_text:
                        equation_text = equation_text.split(": f(x) = ")[1]

                    # Create text item for equation
                    eq_text = pg.TextItem(
                        text=equation_text, 
                        color=(0, 0, 0),     # Black text by default
                        fill=pg.mkBrush(255, 255, 255, 180),  # Semi-transparent white background
                        anchor=(0, 0)  # Top-left anchored
                    )

                    # Adjust text color based on background
                    bg_color = self.background_color_combo.currentText().lower()
                    if bg_color in ["black", "darkgray", "red", "green", "blue"]:
                        eq_text.setColor((255, 255, 255))  # White text for dark backgrounds

                    # Position in top left with small margin
                    x_min = float(self.x_min_edit.text())
                    x_max = float(self.x_max_edit.text())
                    y_min = float(self.y_min_edit.text())
                    y_max = float(self.y_max_edit.text())

                    # Calculate position (5% from left edge, 5% from top)
                    x_pos = x_min + (x_max - x_min) * 0.05
                    y_pos = y_max - (y_max - y_min) * 0.05

                    eq_text.setPos(x_pos, y_pos)

                    # Set font size and style
                    font = eq_text.textItem.font()
                    font.setPointSize(10)
                    font.setBold(True)
                    eq_text.setFont(font)

                    self.plot_widget.addItem(eq_text)

        # Set plot ranges and grid
        self.plot_widget.setXRange(float(self.x_min_edit.text()), float(self.x_max_edit.text()))
        self.plot_widget.setYRange(float(self.y_min_edit.text()), float(self.y_max_edit.text()))

        # Show grid without specifying color (will use default)
        self.plot_widget.showGrid(x=True, y=True, alpha=0.5) # Adjusted alpha from 5th

        # Apply selected axis color with support for "Light Gray"
        axis_color = self.axis_color_combo.currentText().lower().replace(" ", "")
        if axis_color == "lightgray":
            axis_color = (200, 200, 200)  # RGB for light gray
        self.plot_widget.getAxis('bottom').setPen(pg.mkPen(color=axis_color, width=1))
        self.plot_widget.getAxis('left').setPen(pg.mkPen(color=axis_color, width=1))

        # Apply tick label color with support for "Light Gray"
        tick_color_name = self.tick_label_color_combo.currentText().lower().replace(" ", "")
        tick_pg_color = self.get_color_from_name(tick_color_name) if tick_color_name != "lightgray" else (200,200,200)

        bottom_axis = self.plot_widget.getAxis('bottom')
        left_axis = self.plot_widget.getAxis('left')
        # Ensure tickFont style is preserved if already set by apply_plot_settings
        current_bottom_tick_font = bottom_axis.style.get('tickFont', QFont())
        current_left_tick_font = left_axis.style.get('tickFont', QFont())
        bottom_axis.setStyle(tickFont=current_bottom_tick_font) # Re-apply or ensure it's set
        left_axis.setStyle(tickFont=current_left_tick_font)   # Re-apply or ensure it's set
        bottom_axis.setTextPen(pg.mkPen(color=tick_pg_color))
        left_axis.setTextPen(pg.mkPen(color=tick_pg_color))
        self.plot_widget.addLegend()

    def calculate_label_positions(self, x, y, index):
        """Calculate positions for labels to avoid overlap"""
        positions = []
        y_range = max(y) - min(y)
        offset = y_range * 0.02  # 2% of y range as offset
        for i, (xi, yi) in enumerate(zip(x, y)):
            # Check for nearby points
            nearby = False
            for j, (xj, yj) in enumerate(zip(x, y)):
                if i != j and abs(xi - xj) < 0.1 * (max(x) - min(x)) and abs(yi - yj) < 0.1 * y_range:
                    nearby = True
                    break
            # Adjust y position based on nearby points
            if nearby:
                # Alternate between above and below for nearby points
                y_offset = offset if i % 2 == 0 else -offset
            else:
                y_offset = offset
            positions.append((xi, yi + y_offset))
        return positions

    def create_colored_scatter(self, x, y, z, index, point_size, symbol):
        # Convert z to numeric if possible, handling non-numeric cases
        z_numeric = pd.to_numeric(z, errors='coerce')
        scatter = None

        # Remove any existing colorbar
        for item in self.plot_widget.items():
            if isinstance(item, pg.ColorBarItem):
                self.plot_widget.removeItem(item)

        if np.isnan(z_numeric).all():  # Non-numeric z-data (categorical)
            unique_z = np.unique(z)
            color_map = {val: pg.intColor(i, len(unique_z)) for i, val in enumerate(unique_z)}
            colors = [color_map[val] for val in z]

            scatter = pg.ScatterPlotItem(
                x=x, y=y, size=point_size, symbol=symbol, pen=pg.mkPen(None),
                brush=[pg.mkBrush(c) for c in colors], hoverable=True,
                tip=lambda x, y, data, i: f"x: {x:.2f}\ny: {y:.2f}\nz: {z[int(i)]}\nindex: {index[int(i)] if index is not None else int(i)}"
            )
        else:  # Numeric z-data
            z_valid = z_numeric[~np.isnan(z_numeric)]  # Remove NaN values for min/max calculation
            if len(z_valid) > 0:
                z_min = np.min(z_valid)
                z_max = np.max(z_valid)
                cmap = pg.colormap.get(self.colormap_combo.currentText())

                if z_min == z_max:  # Single value case
                    colors = [cmap.map(0.5, mode='qcolor')] * len(z_numeric)
                else:
                    z_norm = (z_numeric - z_min) / (z_max - z_min)
                    z_norm = np.nan_to_num(z_norm, nan=0.0)  # Handle NaN values
                    colors = cmap.map(z_norm, mode='qcolor')

                brushes = [pg.mkBrush(c) for c in colors]

                scatter = pg.ScatterPlotItem(
                    x=x, y=y, size=point_size, symbol=symbol, pen=pg.mkPen(None),
                    brush=brushes, data=z_numeric, hoverable=True,
                    tip=lambda x, y, data, i: f"x: {x:.2f}\ny: {y:.2f}\nz: {z[int(i)]}\nindex: {index[int(i)] if index is not None else int(i)}"
                )

                # Add colorbar with improved positioning
                color_bar = pg.ColorBarItem(
                    values=(z_min, z_max),
                    colorMap=cmap,
                    width=25,
                    interactive=False,
                    orientation='right',
                    label=f"{self.z_data_combo.currentText()}",
                    labelOpts={'color': self.tick_label_color_combo.currentText().lower().replace(" ", "")}
                )
                color_bar.setImageItem(scatter)
                self.plot_widget.addItem(color_bar)
            else:
                # Fallback for all-NaN case
                color_name = self.single_color_combo.currentText().lower().replace(" ", "")
                color = self.get_color_from_name(color_name)

                scatter = pg.ScatterPlotItem(
                    x=x, y=y, size=point_size, symbol=symbol, pen=pg.mkPen(None),
                    brush=pg.mkBrush(color), hoverable=True,
                    tip=lambda x, y, data, i: f"x: {x:.2f}\ny: {y:.2f}\nindex: {index[int(i)] if index is not None else int(i)}"
                )

        return scatter

    def get_symbol_for_shape(self, shape_name):
        """Convert shape name to pyqtgraph symbol character"""
        shape_map = {
            'circle': 'o',
            'square': 's',
            'diamond': 'd',
            'triangle': 't',
            'star': 'star',
            'pentagon': 'p',
            'hexagon': 'h'
        }
        return shape_map.get(shape_name, 'o')  # Default to circle if not found

    def get_color_from_name(self, color_name):
        """Convert color name to RGB tuple or Qt color name"""
        color_map = {
            'blue': (0, 0, 255),
            'red': (255, 0, 0),
            'green': (0, 128, 0),
            'black': (0, 0, 0),
            'cyan': (0, 255, 255),
            'lightgray': (200, 200, 200),
            'magenta': (255, 0, 255),
            'yellow': (255, 255, 0),
            'orange': (255, 165, 0),
            'purple': (128, 0, 128)
        }
        return color_map.get(color_name, (0, 0, 255))  # Default to blue if not found

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CurveFittingTool()
    window.show()
    sys.exit(app.exec_())
