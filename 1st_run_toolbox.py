# -*- coding: utf-8 -*-
"""
Created on Wed Mar 12 14:42:20 2025

@author: devri.agustianto
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
import pandas as pd
from scipy.optimize import curve_fit
from matplotlib.figure import Figure
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg

class CurveFittingTool(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Curve Fitting Tool")
        self.geometry("800x600")

        # Initial sample data
        x = np.linspace(0, 50, 100)
        y = 2 * x + 3 + np.random.normal(0, 5, 100)
        z = 0.01 * x**3 - 0.5 * x**2 + 10 * x + 100 + np.random.normal(0, 100, 100)
        self.data = {'x': x, 'y': y, 'z': z}

        # --- Menu Bar ---
        menubar = tk.Menu(self)
        filemenu = tk.Menu(menubar, tearoff=0)
        filemenu.add_command(label="Import Data", command=self.import_data)
        menubar.add_cascade(label="File", menu=filemenu)
        self.config(menu=menubar)

        # --- Control Panel (Left) ---
        control_frame = tk.Frame(self)
        control_frame.pack(side=tk.LEFT, padx=10, pady=10, fill=tk.Y)

        # Fit name
        tk.Label(control_frame, text="Fit name:").pack()
        self.fit_name_entry = tk.Entry(control_frame)
        self.fit_name_entry.pack()
        self.fit_name_entry.insert(0, "untitled fit 1")

        # Fit type
        tk.Label(control_frame, text="Fit type:").pack()
        self.fit_type_var = tk.StringVar(value="Polynomial")
        fit_types = ["Linear", "Power", "Exponential", "Polynomial"]
        self.fit_type_dropdown = ttk.Combobox(control_frame, textvariable=self.fit_type_var, values=fit_types)
        self.fit_type_dropdown.pack()
        self.fit_type_dropdown.bind("<<ComboboxSelected>>", self.on_fit_type_change)

        # Degree (for polynomial)
        self.degree_label = tk.Label(control_frame, text="Degree:")
        self.degree_label.pack()
        self.degree_var = tk.StringVar(value="3")
        degrees = [str(i) for i in range(1, 6)]
        self.degree_dropdown = ttk.Combobox(control_frame, textvariable=self.degree_var, values=degrees)
        self.degree_dropdown.pack()
        self.degree_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Auto fit checkbox
        self.auto_fit_var = tk.BooleanVar(value=True)
        self.auto_fit_check = tk.Checkbutton(control_frame, text="Auto fit", variable=self.auto_fit_var)
        self.auto_fit_check.pack()

        # X data
        tk.Label(control_frame, text="X data:").pack()
        self.x_data_var = tk.StringVar(value="x")
        self.x_data_dropdown = ttk.Combobox(control_frame, textvariable=self.x_data_var)
        self.x_data_dropdown['values'] = list(self.data.keys())
        self.x_data_dropdown.pack()
        self.x_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Y data
        tk.Label(control_frame, text="Y data:").pack()
        self.y_data_var = tk.StringVar(value="z")
        self.y_data_dropdown = ttk.Combobox(control_frame, textvariable=self.y_data_var)
        self.y_data_dropdown['values'] = list(self.data.keys())
        self.y_data_dropdown.pack()
        self.y_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Fit and Stop buttons
        self.fit_button = tk.Button(control_frame, text="Fit", command=self.perform_fit)
        self.fit_button.pack(pady=5)
        self.stop_button = tk.Button(control_frame, text="Stop", state="disabled")
        self.stop_button.pack()

        # Results frame
        results_frame = tk.LabelFrame(control_frame, text="Results")
        results_frame.pack(pady=10, fill=tk.X)
        self.equation_label = tk.Label(results_frame, text="", wraplength=200)
        self.equation_label.pack()
        self.param_labels = []
        for i in range(5):  # Up to 5 parameters
            label = tk.Label(results_frame, text="")
            label.pack()
            self.param_labels.append(label)
        self.sse_label = tk.Label(results_frame, text="SSE: ")
        self.sse_label.pack()
        self.rsquare_label = tk.Label(results_frame, text="R-square: ")
        self.rsquare_label.pack()
        self.rmse_label = tk.Label(results_frame, text="RMSE: ")
        self.rmse_label.pack()

        # --- Plot Frame (Right) ---
        plot_frame = tk.Frame(self)
        plot_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        self.figure = Figure(figsize=(5, 4), dpi=100)
        self.ax = self.figure.add_subplot(111)
        self.canvas = FigureCanvasTkAgg(self.figure, master=plot_frame)
        self.canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

        # Initial setup
        self.on_fit_type_change()  # Set initial visibility
        self.perform_fit()

    def import_data(self):
        """Import data from Excel or CSV and update dropdowns"""
        file_path = filedialog.askopenfilename(
            title="Select Excel or CSV file",
            filetypes=[("Excel files", "*.xlsx *.xls"), ("CSV files", "*.csv")]
        )
        if not file_path:
            return

        try:
            if file_path.endswith('.csv'):
                df = pd.read_csv(file_path)
                self.data.clear()
                for column in df.columns:
                    self.data[column] = df[column].dropna().values
            else:
                # Excel file: select sheet
                excel_file = pd.ExcelFile(file_path)
                sheet_names = excel_file.sheet_names
                if not sheet_names:
                    messagebox.showerror("Error", "No sheets found in the Excel file.")
                    return
                sheet_name = tk.StringVar(value=sheet_names[0])
                sheet_dialog = tk.Toplevel(self)
                sheet_dialog.title("Select Sheet")
                tk.Label(sheet_dialog, text="Select sheet:").pack()
                sheet_dropdown = ttk.Combobox(sheet_dialog, textvariable=sheet_name, values=sheet_names)
                sheet_dropdown.pack()
                tk.Button(sheet_dialog, text="OK", command=sheet_dialog.destroy).pack()
                sheet_dialog.wait_window()
                selected_sheet = sheet_name.get()
                df = pd.read_excel(file_path, sheet_name=selected_sheet)
                self.data.clear()
                for column in df.columns:
                    self.data[column] = df[column].dropna().values

            # Update dropdowns
            columns = list(self.data.keys())
            if columns:
                self.x_data_dropdown['values'] = columns
                self.y_data_dropdown['values'] = columns
                self.x_data_var.set(columns[0])
                self.y_data_var.set(columns[1] if len(columns) > 1 else columns[0])
                self.perform_fit()
            else:
                messagebox.showerror("Error", "No valid columns found in the file.")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load file: {e}")

    def on_fit_type_change(self, *args):
        """Show/hide degree dropdown based on fit type"""
        fit_type = self.fit_type_var.get()
        if fit_type == "Polynomial":
            self.degree_label.pack()
            self.degree_dropdown.pack()
        else:
            self.degree_label.pack_forget()
            self.degree_dropdown.pack_forget()
        if self.auto_fit_var.get():
            self.perform_fit()

    def on_param_change(self, *args):
        """Trigger fit if auto fit is enabled"""
        if self.auto_fit_var.get():
            self.perform_fit()

    def perform_fit(self):
        """Perform fitting based on selected type and update plot and results"""
        try:
            x_key = self.x_data_var.get()
            y_key = self.y_data_var.get()
            if x_key not in self.data or y_key not in self.data:
                return

            x = self.data[x_key]
            y = self.data[y_key]

            # Check for numeric data
            if not (np.issubdtype(x.dtype, np.number) and np.issubdtype(y.dtype, np.number)):
                messagebox.showerror("Error", "Selected X and Y data must be numeric.")
                return

            min_len = min(len(x), len(y))
            x = x[:min_len]
            y = y[:min_len]

            fit_type = self.fit_type_var.get()
            if fit_type == "Polynomial":
                degree = int(self.degree_var.get())
                coeffs = np.polyfit(x, y, degree)
                poly = np.poly1d(coeffs)
                fitted_y = poly(x)
                equation = f"Polynomial (degree {degree}): f(x) = " + " + ".join(
                    [f"{c:.2f}x^{degree-i}" if i < degree else f"{c:.2f}" for i, c in enumerate(coeffs)]
                )
                params = [f"p{i+1} = {c:.2f}" for i, c in enumerate(coeffs)]
            else:
                if fit_type == "Linear":
                    def model(x, a, b):
                        return a * x + b
                    equation_template = "Linear: f(x) = a x + b"
                    param_names = ["a", "b"]
                elif fit_type == "Power":
                    def model(x, a, b):
                        return a * x**b
                    equation_template = "Power: f(x) = a x^b"
                    param_names = ["a", "b"]
                elif fit_type == "Exponential":
                    def model(x, a, b):
                        return a * np.exp(b * x)
                    equation_template = "Exponential: f(x) = a e^(b x)"
                    param_names = ["a", "b"]

                popt, _ = curve_fit(model, x, y, maxfev=10000)
                fitted_y = model(x, *popt)
                equation = equation_template.replace("a", f"{popt[0]:.2f}").replace("b", f"{popt[1]:.2f}")
                params = [f"{name} = {val:.2f}" for name, val in zip(param_names, popt)]

            # Update equation and parameters
            self.equation_label.config(text=equation)
            for i, param in enumerate(params):
                self.param_labels[i].config(text=param)
            for i in range(len(params), 5):
                self.param_labels[i].config(text="")

            # Calculate goodness-of-fit metrics
            residuals = y - fitted_y
            sse = np.sum(residuals**2)
            sst = np.sum((y - np.mean(y))**2)
            rsquare = 1 - (sse / sst) if sst != 0 else 0
            rmse = np.sqrt(sse / len(y))
            self.sse_label.config(text=f"SSE: {sse:.2f}")
            self.rsquare_label.config(text=f"R-square: {rsquare:.4f}")
            self.rmse_label.config(text=f"RMSE: {rmse:.2f}")

            # Update plot
            self.ax.clear()
            self.ax.scatter(x, y, color='black', label='Data')
            x_plot = np.linspace(min(x), max(x), 100)
            if fit_type == "Polynomial":
                y_plot = poly(x_plot)
            else:
                y_plot = model(x_plot, *popt)
            self.ax.plot(x_plot, y_plot, 'b-', label='Fit')
            self.ax.grid(True)
            self.ax.set_xlim(min(x), max(x))
            self.ax.set_ylim(min(y), max(y))
            self.ax.legend()
            self.canvas.draw()

        except Exception as e:
            messagebox.showerror("Error", f"Fitting failed: {e}")

if __name__ == "__main__":
    app = CurveFittingTool()
    app.mainloop()