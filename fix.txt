The second problem in your code is that text labels (imported as index data) are not displayed directly on the plot—currently, they only appear in tooltips when hovering over points. To fix this, we need to add permanent text labels to the plot using the index data. I'll modify the `update_plot` method to include text labels when index data is available.

Here's the fixed version of the relevant code:

```python
def update_plot(self, x, y, z, index, x_plot, y_plot, y_lower, y_upper):
    self.plot_widget.clear()
    point_size = float(self.point_size_edit.text())
    
    # Create scatter plot
    if z is not None and len(z) > 0:
        scatter_item = self.create_colored_scatter(x, y, z, index, point_size)
    else:
        scatter_item = pg.ScatterPlotItem(
            x=x, y=y, size=point_size, pen=pg.mkPen(None), 
            brush=pg.mkBrush('b'), hoverable=True,
            tip=lambda x, y, i: f"x: {x:.2f}\ny: {y:.2f}\nindex: {index[int(i)] if index is not None else int(i)}"
        )
    
    self.plot_widget.addItem(scatter_item)
    
    # Add text labels if index data is provided
    if index is not None and len(index) > 0:
        for xi, yi, label in zip(x, y, index):
            # Create text item for each point
            text_item = pg.TextItem(
                text=str(label), 
                anchor=(0.5, -0.5),  # Position above point
                color=(0, 0, 0),     # Black text
                fill=pg.mkBrush(255, 255, 255, 100)  # Semi-transparent white background
            )
            text_item.setPos(xi, yi)
            # Optional: Adjust font size
            font = text_item.textItem.font()
            font.setPointSize(8)  # Adjust as needed
            text_item.setFont(font)
            self.plot_widget.addItem(text_item)
    
    # Plot the fitted curve
    if y_plot is not None:
        fit_curve = pg.PlotDataItem(x_plot, y_plot, pen=pg.mkPen('r', width=2), name='Fit')
        self.plot_widget.addItem(fit_curve)
        
        if self.show_uncertainty_check.isChecked():
            uncertainty_fill = pg.FillBetweenItem(
                pg.PlotDataItem(x_plot, y_lower, pen=pg.mkPen('r', width=0)),
                pg.PlotDataItem(x_plot, y_upper, pen=pg.mkPen('r', width=0)),
                brush=pg.mkBrush((255, 0, 0, 50))
            )
            self.plot_widget.addItem(uncertainty_fill)
    
    # Set plot ranges and grid
    self.plot_widget.setXRange(float(self.x_min_edit.text()), float(self.x_max_edit.text()))
    self.plot_widget.setYRange(float(self.y_min_edit.text()), float(self.y_max_edit.text()))
    self.plot_widget.showGrid(x=True, y=True)
    self.plot_widget.addLegend()
```

### Key Changes and Explanations:
1. **Adding Text Labels:**
   - A new block checks if `index` data exists and is not empty.
   - For each data point `(xi, yi)`, a `pg.TextItem` is created with the corresponding `label` from the index data.
   - The `anchor=(0.5, -0.5)` positions the label above the point (centered horizontally, bottom of text at the point).

2. **Styling the Labels:**
   - `color=(0, 0, 0)` sets the text to black.
   - `fill=pg.mkBrush(255, 255, 255, 100)` adds a semi-transparent white background to make labels readable against the plot.
   - Font size is set to 8 points (adjustable as needed).

3. **Integration with Existing Code:**
   - The text labels are added after the scatter plot but before the fit curve, ensuring they appear in the correct layer.
   - The rest of the method (fit curve, uncertainty, ranges) remains unchanged.

### How to Test:
1. **Prepare Data:**
   - Import a dataset (CSV or Excel) with an index column (e.g., a column with labels like "Point1", "1", or any text/numeric values).
   - In the `ImportDataDialog`, select this column as the "Index Column" (not "None").

2. **Run the Fit:**
   - After importing data and performing the fit, you should see:
     - Scatter points with their corresponding index labels displayed above each point.
     - The labels remain visible (not just in tooltips).

3. **Verify:**
   - If labels don’t appear, check:
     - The index column was correctly selected in the import dialog.
     - The `index` variable in `update_plot` contains data (print it to debug if needed).

### Optional Enhancements:
- **Toggle Labels:**
  Add a checkbox to the GUI to show/hide labels:
  ```python
  # In __init__:
  self.show_labels_check = QCheckBox("Show Labels")
  self.show_labels_check.setChecked(True)
  control_layout.addWidget(self.show_labels_check)
  
  # In update_plot, wrap label addition:
  if index is not None and len(index) > 0 and self.show_labels_check.isChecked():
      # ... label creation code ...
  ```

- **Adjust Label Position:**
  Modify the position if labels overlap with points:
  ```python
  text_item.setPos(xi, yi + point_size / 10)  # Small offset above point
  ```

- **Handle Overcrowding:**
  For dense plots, limit the number of labels:
  ```python
  if index is not None and len(index) > 0:
      step = max(1, len(index) // 20)  # Show ~20 labels max
      for i, (xi, yi, label) in enumerate(zip(x, y, index)):
          if i % step == 0:  # Show every nth label
              text_item = pg.TextItem(text=str(label), anchor=(0.5, -0.5))
              text_item.setPos(xi, yi)
              self.plot_widget.addItem(text_item)
  ```

These changes will ensure your index labels are displayed directly on the plot. Let me know if you need further assistance integrating this fix or handling specific edge cases!