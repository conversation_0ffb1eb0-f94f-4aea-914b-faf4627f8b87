#!/usr/bin/env python3
"""
Test script to verify that the polynomial fitting fix works correctly.
This script tests the polynomial fitting functionality without the GUI.
"""

import sys
import numpy as np
import warnings
from scipy.optimize import curve_fit

def test_polynomial_fitting():
    """Test polynomial fitting functionality"""
    print("Testing polynomial fitting functionality...")
    
    # Generate test data (quadratic function with noise)
    x = np.linspace(0, 10, 50)
    y_true = 2 * x**2 + 3 * x + 1  # True quadratic function
    noise = np.random.normal(0, 5, len(x))
    y = y_true + noise
    
    # Test polynomial fitting (degree 2)
    degree = 2
    try:
        coeffs = np.polyfit(x, y, degree)
        poly_func = np.poly1d(coeffs)
        
        fitted_y = poly_func(x)
        x_plot = np.linspace(0, 10, 100)
        y_plot = poly_func(x_plot)
        
        # Calculate R-squared
        ss_res = np.sum((y - fitted_y) ** 2)
        ss_tot = np.sum((y - np.mean(y)) ** 2)
        r_squared = 1 - (ss_res / ss_tot)
        
        print(f"✓ Polynomial fitting successful!")
        print(f"  Degree: {degree}")
        print(f"  Coefficients: {coeffs}")
        print(f"  R-squared: {r_squared:.4f}")
        print(f"  Expected coefficients: [2, 3, 1] (approximately)")
        
        # Check if coefficients are reasonable (within 20% of expected)
        expected = [2, 3, 1]
        tolerance = 0.5  # Allow 50% tolerance due to noise
        
        success = True
        for i, (actual, exp) in enumerate(zip(coeffs, expected)):
            if abs(actual - exp) > tolerance * abs(exp):
                print(f"  Warning: Coefficient {i} ({actual:.3f}) differs significantly from expected ({exp})")
                success = False
        
        if success:
            print("✓ Polynomial coefficients are within expected range!")
        
        return True
        
    except Exception as e:
        print(f"✗ Polynomial fitting failed: {e}")
        return False

def test_custom_function_parsing():
    """Test custom function parsing"""
    print("\nTesting custom function parsing...")
    
    try:
        import re
        from sympy import symbols, sympify, lambdify
        
        # Test function string
        func_str = "a*x**2 + b*x + c"
        
        # Extract parameters
        param_pattern = r'\b[a-z]\b'
        params_found = list(set(re.findall(param_pattern, func_str)))
        params_found.sort()
        
        print(f"  Function: {func_str}")
        print(f"  Parameters found: {params_found}")
        
        # Create symbolic function
        x_sym = symbols('x')
        param_symbols = symbols(params_found)
        
        expr = sympify(func_str)
        func = lambdify([x_sym] + list(param_symbols), expr, 'numpy')
        
        # Test the function
        x_test = np.array([1, 2, 3])
        params_test = [1, 2, 3]  # a=1, b=2, c=3
        result = func(x_test, *params_test)
        expected = 1*x_test**2 + 2*x_test + 3  # 1*x^2 + 2*x + 3
        
        if np.allclose(result, expected):
            print("✓ Custom function parsing successful!")
            return True
        else:
            print(f"✗ Custom function parsing failed: got {result}, expected {expected}")
            return False
            
    except Exception as e:
        print(f"✗ Custom function parsing failed: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("Testing Polynomial Fitting Fix")
    print("=" * 60)
    
    # Suppress warnings for cleaner output
    warnings.filterwarnings('ignore')
    
    test1_passed = test_polynomial_fitting()
    test2_passed = test_custom_function_parsing()
    
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    print("=" * 60)
    print(f"Polynomial fitting: {'PASS' if test1_passed else 'FAIL'}")
    print(f"Custom function parsing: {'PASS' if test2_passed else 'FAIL'}")
    
    if test1_passed and test2_passed:
        print("\n✓ All tests passed! The polynomial fitting fix is working correctly.")
        sys.exit(0)
    else:
        print("\n✗ Some tests failed. Please check the implementation.")
        sys.exit(1)
