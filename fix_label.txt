Recommended Fix
Given that your code already calls showGrid(), the simplest and most maintainable solution is to use its alpha parameter and remove the erroneous setGrid() calls. Update the update_plot() method as follows:

def update_plot(self, x, y, z, index, x_plot, y_plot, y_lower, y_upper):
    self.plot_widget.clear()
    point_size = float(self.point_size_edit.text())
    # ... (scatter plot and curve plotting code remains unchanged) ...

    # Set plot ranges and grid
    self.plot_widget.setXRange(float(self.x_min_edit.text()), float(self.x_max_edit.text()))
    self.plot_widget.setYRange(float(self.y_min_edit.text()), float(self.y_max_edit.text()))
    
    # Use selected grid color (not directly applied, noted as limitation)
    grid_color = self.grid_color_combo.currentText().lower().replace(" ", "")
    self.plot_widget.showGrid(x=True, y=True, alpha=0.8)  # Set grid with opacity
    
    # Apply selected axis color
    axis_color = self.axis_color_combo.currentText().lower().replace(" ", "")
    self.plot_widget.getAxis('bottom').setPen(pg.mkPen(color=axis_color, width=1))
    self.plot_widget.getAxis('left').setPen(pg.mkPen(color=axis_color, width=1))
    
    self.plot_widget.addLegend()


Conclusion
The error persists because the setGrid() method was still being called with an unsupported QPen argument after your fix. By switching to showGrid(x=True, y=True, alpha=0.8) and removing the setGrid() calls, you align the code with pyqtgraph’s API, eliminating the TypeError. While this sacrifices custom grid styling, it resolves the immediate issue. If custom grid appearance remains critical, consider exploring advanced workarounds like subclassing AxisItem, but for now, this fix ensures your script runs without errors.