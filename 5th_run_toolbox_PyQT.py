# -*- coding: utf-8 -*-
"""
Created on Sat Mar 15 11:40:13 2025

@author: mutia
"""

import sys
import numpy as np
import pandas as pd
import traceback
import warnings
from PyQt5.QtWidgets import (QMainWindow, QApplication, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QComboBox, QPushButton, QLineEdit, QCheckBox,
                             QGroupBox, QGridLayout, QFileDialog, QMessageBox, QDialog,
                             QVBoxLayout as QVBoxLayoutDialog, QHBoxLayout as QHBoxLayoutDialog,
                             QTableWidget, QTableWidgetItem, QScrollArea, QDialogButtonBox,
                             QSpinBox)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont
from scipy.optimize import curve_fit
from sympy import sympify, symbols, lambdify
import pyqtgraph as pg

class ImportDataDialog(QDialog):
    def __init__(self, parent, df):
        super().__init__(parent)
        self.setWindowTitle("Select Data Ranges")
        self.setGeometry(100, 100, 800, 500)
        self.df = df

        layout = QVBoxLayoutDialog(self)
        preview_frame = QWidget()
        preview_layout = QVBoxLayoutDialog(preview_frame)
        self.table = QTableWidget()
        self.table.setRowCount(len(df))
        self.table.setColumnCount(len(df.columns) + 1)
        self.table.setHorizontalHeaderLabels(["Row"] + [f"{i+1}: {col}" for i, col in enumerate(df.columns)])
        self.table.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOn)
        self.table.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOn)

        for i in range(len(df)):
            self.table.setItem(i, 0, QTableWidgetItem(str(i + 1)))
            for j, value in enumerate(df.iloc[i]):
                self.table.setItem(i, j + 1, QTableWidgetItem(str(value)))

        self.table.resizeColumnsToContents()
        preview_layout.addWidget(self.table)
        scroll = QScrollArea()
        scroll.setWidget(preview_frame)
        scroll.setWidgetResizable(True)
        layout.addWidget(scroll)

        layout.addWidget(QLabel("Row Range (e.g., 1-10 or leave blank for all):"))
        self.row_range_entry = QLineEdit()
        layout.addWidget(self.row_range_entry)

        layout.addWidget(QLabel("X Column:"))
        layout.addWidget(QLabel("Note: Column A = 1, B = 2, etc."))
        self.x_col_var = QComboBox()
        self.x_col_var.addItems([f"{i+1}: {col}" for i, col in enumerate(df.columns)])
        first_numeric_idx = self.find_first_numeric_column(df)
        self.x_col_var.setCurrentIndex(first_numeric_idx)
        layout.addWidget(self.x_col_var)

        layout.addWidget(QLabel("Y Column:"))
        layout.addWidget(QLabel("Note: Column A = 1, B = 2, etc."))
        self.y_col_var = QComboBox()
        self.y_col_var.addItems([f"{i+1}: {col}" for i, col in enumerate(df.columns)])
        second_numeric_idx = min(first_numeric_idx + 1, len(df.columns) - 1)
        if not pd.to_numeric(df.iloc[:, second_numeric_idx], errors='coerce').notna().any():
            second_numeric_idx = first_numeric_idx
        self.y_col_var.setCurrentIndex(second_numeric_idx)
        layout.addWidget(self.y_col_var)

        layout.addWidget(QLabel("Z Column (color key, optional):"))
        layout.addWidget(QLabel("Select 'None' to use a single color."))
        self.z_col_var = QComboBox()
        self.z_col_var.addItems(["None"] + [f"{i+1}: {col}" for i, col in enumerate(df.columns)])
        self.z_col_var.setCurrentIndex(0)
        layout.addWidget(self.z_col_var)

        layout.addWidget(QLabel("Index Column (optional for point labels):"))
        layout.addWidget(QLabel("Select 'None' to disable point labels."))
        self.index_col_var = QComboBox()
        self.index_col_var.addItems(["None"] + [f"{i+1}: {col}" for i, col in enumerate(df.columns)])
        self.index_col_var.setCurrentIndex(0)
        layout.addWidget(self.index_col_var)

        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)

    def find_first_numeric_column(self, df):
        for i, col in enumerate(df.columns):
            if pd.to_numeric(df[col], errors='coerce').notna().any():
                return i
        return 0

    def get_selected_data(self):
        row_range = self.row_range_entry.text()
        x_col = self.x_col_var.currentText().split(":")[0].strip()
        y_col = self.y_col_var.currentText().split(":")[0].strip()
        z_col = self.z_col_var.currentText()
        index_col = self.index_col_var.currentText()
        return row_range, x_col, y_col, z_col, index_col

class CurveFittingTool(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Curve Fitting Tool")
        self.setGeometry(100, 100, 900, 700)

        x = np.linspace(0, 50, 100)
        y = 2 * x + 3 + np.random.normal(0, 5, 100)
        z = np.random.choice(['A', 'B', 'C'], 100)
        index = np.arange(100)
        self.data = {'x': x, 'y': y, 'z': z, 'index': index}

        self.custom_functions = {
            "Logistic": {"model": lambda x, L, k, x0: L / (1 + np.exp(-k * (x - x0))), "equation": "f(x) = L / (1 + e^(-k(x - x0)))", "params": ["L", "k", "x0"]},
            "Gaussian": {"model": lambda x, a, b, c: a * np.exp(-((x - b) ** 2) / (2 * c ** 2)), "equation": "f(x) = a * e^(-(x - b)^2 / (2c^2))", "params": ["a", "b", "c"]},
            "Sinusoidal": {"model": lambda x, a, b, c: a * np.sin(b * x + c), "equation": "f(x) = a * sin(bx + c)", "params": ["a", "b", "c"]}
        }

        self.x_min = 0
        self.x_max = 50
        self.y_min = -10
        self.y_max = 120
        self.point_size = 10
        self.colormap = 'viridis'

        main_widget = QWidget(self)
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)

        control_widget = QWidget()
        control_layout = QVBoxLayout(control_widget)
        main_layout.addWidget(control_widget)

        self.select_file_btn = QPushButton("Select Data File")
        self.select_file_btn.clicked.connect(self.import_data)
        control_layout.addWidget(self.select_file_btn)

        control_layout.addWidget(QLabel("Fit name:"))
        self.fit_name_edit = QLineEdit("untitled fit 1")
        control_layout.addWidget(self.fit_name_edit)

        control_layout.addWidget(QLabel("Fit type:"))
        self.fit_type_combo = QComboBox()
        self.fit_type_combo.addItems(["Linear", "Power", "Exponential", "Polynomial", "Custom", "Pre-defined Custom"])
        self.fit_type_combo.currentTextChanged.connect(self.on_fit_type_change)
        control_layout.addWidget(self.fit_type_combo)

        self.custom_func_label = QLabel("Custom function (e.g., a*x + b):")
        self.custom_func_edit = QLineEdit("a*x + b")
        self.custom_func_widget = QWidget()
        custom_layout = QVBoxLayout(self.custom_func_widget)
        custom_layout.addWidget(self.custom_func_label)
        custom_layout.addWidget(self.custom_func_edit)

        self.predefined_func_label = QLabel("Pre-defined function:")
        self.predefined_func_combo = QComboBox()
        self.predefined_func_combo.addItems(list(self.custom_functions.keys()))
        self.predefined_func_widget = QWidget()
        predefined_layout = QVBoxLayout(self.predefined_func_widget)
        predefined_layout.addWidget(self.predefined_func_label)
        predefined_layout.addWidget(self.predefined_func_combo)

        self.degree_label = QLabel("Degree:")
        self.degree_combo = QComboBox()
        self.degree_combo.addItems([str(i) for i in range(1, 6)])
        self.degree_combo.setCurrentText("3")
        self.degree_combo.currentTextChanged.connect(self.on_param_change)
        self.degree_widget = QWidget()
        degree_layout = QVBoxLayout(self.degree_widget)
        degree_layout.addWidget(self.degree_label)
        degree_layout.addWidget(self.degree_combo)

        control_layout.addWidget(self.custom_func_widget)
        control_layout.addWidget(self.predefined_func_widget)
        control_layout.addWidget(self.degree_widget)

        self.auto_fit_check = QCheckBox("Auto fit")
        self.auto_fit_check.stateChanged.connect(self.on_param_change)
        control_layout.addWidget(self.auto_fit_check)

        self.show_uncertainty_check = QCheckBox("Show uncertainty (±2*SER)")
        self.show_uncertainty_check.setChecked(True)
        self.show_uncertainty_check.stateChanged.connect(self.perform_fit)
        control_layout.addWidget(self.show_uncertainty_check)

        self.show_labels_check = QCheckBox("Show Point Labels")
        self.show_labels_check.setChecked(True)
        self.show_labels_check.stateChanged.connect(self.perform_fit)
        control_layout.addWidget(self.show_labels_check)

        self.x_data_combo = QComboBox()
        self.y_data_combo = QComboBox()
        self.z_data_combo = QComboBox()
        self.index_data_combo = QComboBox()
        for combo in [self.x_data_combo, self.y_data_combo, self.z_data_combo, self.index_data_combo]:
            combo.addItems(list(self.data.keys()))
            combo.currentTextChanged.connect(self.on_param_change)
        self.x_data_combo.setCurrentText("x")
        self.y_data_combo.setCurrentText("y")
        self.z_data_combo.setCurrentText("z")
        self.index_data_combo.setCurrentText("index")

        control_layout.addWidget(QLabel("X data:"))
        control_layout.addWidget(self.x_data_combo)
        control_layout.addWidget(QLabel("Y data:"))
        control_layout.addWidget(self.y_data_combo)
        control_layout.addWidget(QLabel("Z data (color key):"))
        control_layout.addWidget(self.z_data_combo)
        control_layout.addWidget(QLabel("Index data:"))
        control_layout.addWidget(self.index_data_combo)

        self.fit_btn = QPushButton("Fit")
        self.fit_btn.clicked.connect(self.perform_fit)
        control_layout.addWidget(self.fit_btn)

        results_group = QGroupBox("Results")
        results_layout = QVBoxLayout(results_group)
        self.equation_label = QLabel("")
        self.param_labels = [QLabel("") for _ in range(5)]
        self.sse_label = QLabel("SSE: ")
        self.rsquare_label = QLabel("R-square: ")
        self.rmse_label = QLabel("RMSE: ")
        self.ser_label = QLabel("SER: ")  # Added SER label
        for label in [self.equation_label] + self.param_labels + [self.sse_label, self.rsquare_label, self.rmse_label, self.ser_label]:
            results_layout.addWidget(label)
        control_layout.addWidget(results_group)

        settings_group = QGroupBox("Plot Settings")
        settings_layout = QGridLayout(settings_group)
        self.x_min_edit = QLineEdit("0")
        self.x_max_edit = QLineEdit("50")
        self.y_min_edit = QLineEdit("-10")
        self.y_max_edit = QLineEdit("120")
        self.point_size_edit = QLineEdit("10")
        self.colormap_combo = QComboBox()
        self.colormap_combo.addItems(['viridis', 'plasma', 'inferno', 'magma', 'cividis'])
        self.colormap_combo.setCurrentText('viridis')
        self.tick_step_edit = QLineEdit("5")

        settings_layout.addWidget(QLabel("X Min:"), 0, 0)
        settings_layout.addWidget(self.x_min_edit, 0, 1)
        settings_layout.addWidget(QLabel("X Max:"), 0, 2)
        settings_layout.addWidget(self.x_max_edit, 0, 3)
        settings_layout.addWidget(QLabel("Y Min:"), 1, 0)
        settings_layout.addWidget(self.y_min_edit, 1, 1)
        settings_layout.addWidget(QLabel("Y Max:"), 1, 2)
        settings_layout.addWidget(self.y_max_edit, 1, 3)
        settings_layout.addWidget(QLabel("Point Size:"), 2, 0)
        settings_layout.addWidget(self.point_size_edit, 2, 1)
        settings_layout.addWidget(QLabel("Colormap:"), 3, 0)
        settings_layout.addWidget(self.colormap_combo, 3, 1)
        settings_layout.addWidget(QLabel("Tick Step:"), 4, 0)
        settings_layout.addWidget(self.tick_step_edit, 4, 1)

        self.axis_title_font_type = QComboBox()
        self.axis_title_font_type.addItems(["Arial", "Times New Roman", "Courier New"])
        self.axis_title_font_size = QSpinBox()
        self.axis_title_font_size.setRange(8, 20)
        self.axis_title_font_size.setValue(12)
        self.axis_tick_font_type = QComboBox()
        self.axis_tick_font_type.addItems(["Arial", "Times New Roman", "Courier New"])
        self.axis_tick_font_size = QSpinBox()
        self.axis_tick_font_size.setRange(8, 20)
        self.axis_tick_font_size.setValue(10)

        settings_layout.addWidget(QLabel("Axis Title Font Type:"), 5, 0)
        settings_layout.addWidget(self.axis_title_font_type, 5, 1)
        settings_layout.addWidget(QLabel("Axis Title Font Size:"), 5, 2)
        settings_layout.addWidget(self.axis_title_font_size, 5, 3)
        settings_layout.addWidget(QLabel("Axis Tick Font Type:"), 6, 0)
        settings_layout.addWidget(self.axis_tick_font_type, 6, 1)
        settings_layout.addWidget(QLabel("Axis Tick Font Size:"), 6, 2)
        settings_layout.addWidget(self.axis_tick_font_size, 6, 3)

        self.axis_title_bold_check = QCheckBox("Bold")
        self.axis_title_italic_check = QCheckBox("Italic")
        settings_layout.addWidget(self.axis_title_bold_check, 5, 4)
        settings_layout.addWidget(self.axis_title_italic_check, 5, 5)

        self.axis_tick_bold_check = QCheckBox("Bold") 
        self.axis_tick_italic_check = QCheckBox("Italic")
        settings_layout.addWidget(self.axis_tick_bold_check, 6, 4)
        settings_layout.addWidget(self.axis_tick_italic_check, 6, 5)

        self.x_title_edit = QLineEdit("X")
        self.y_title_edit = QLineEdit("Y")
        settings_layout.addWidget(QLabel("X-axis Title:"), 7, 0)
        settings_layout.addWidget(self.x_title_edit, 7, 1)
        settings_layout.addWidget(QLabel("Y-axis Title:"), 7, 2)
        settings_layout.addWidget(self.y_title_edit, 7, 3)

        self.background_color_combo = QComboBox()
        self.background_color_combo.addItems(["White", "Black", "Light Gray", "Dark Gray", "Red", "Green", "Blue"])
        self.background_color_combo.setCurrentText("White")
        settings_layout.addWidget(QLabel("Background color:"), 8, 0)
        settings_layout.addWidget(self.background_color_combo, 8, 1)

        self.apply_settings_btn = QPushButton("Apply Settings")
        self.apply_settings_btn.clicked.connect(self.apply_plot_settings)
        settings_layout.addWidget(self.apply_settings_btn, 9, 0, 1, 4)
        control_layout.addWidget(settings_group)

        control_layout.addStretch()

        self.plot_widget = pg.PlotWidget()
        main_layout.addWidget(self.plot_widget, stretch=1)

        self.on_fit_type_change()
        self.perform_fit()

        self.colormap_combo.currentTextChanged.connect(self.perform_fit)

    def parse_range(self, range_str, max_value, label):
        if not range_str:
            return slice(None)
        try:
            if '-' in range_str:
                start, end = map(int, range_str.split('-'))
                if start < 1 or end > max_value or start > end:
                    raise ValueError(f"Invalid {label} range. Must be between 1 and {max_value} and start <= end.")
                return slice(start - 1, end)
            else:
                idx = int(range_str) - 1
                if idx < 0 or idx >= max_value:
                    raise ValueError(f"Invalid {label} index. Must be between 1 and {max_value}.")
                return slice(idx, idx + 1)
        except ValueError as e:
            QMessageBox.critical(self, "Error", str(e))
            return None

    def find_first_numeric_column(self, df):
        for i, col in enumerate(df.columns):
            if pd.to_numeric(df[col], errors='coerce').notna().any():
                return i
        return 0

    def import_data(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "Select Data File", "", "Excel/CSV Files (*.xlsx *.xls *.csv)")
        if not file_path:
            return

        try:
            if file_path.endswith(('.csv')):
                df = pd.read_csv(file_path)
                print(f"CSV loaded: {df.shape} rows, columns: {list(df.columns)}")
            else:
                excel_file = pd.ExcelFile(file_path)
                sheet_names = excel_file.sheet_names
                if not sheet_names:
                    QMessageBox.critical(self, "Error", "No sheets found in the Excel file.")
                    return

                sheet_dialog = QDialog(self)
                sheet_dialog.setWindowTitle("Select Sheet")
                layout = QVBoxLayoutDialog(sheet_dialog)
                sheet_var = QComboBox()
                sheet_var.addItems(sheet_names)
                sheet_var.setCurrentIndex(0)
                layout.addWidget(QLabel("Select sheet:"))
                layout.addWidget(sheet_var)
                buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
                buttons.accepted.connect(sheet_dialog.accept)
                buttons.rejected.connect(sheet_dialog.reject)
                layout.addWidget(buttons)
                sheet_dialog.exec_()
                if sheet_dialog.result() == QDialog.Rejected:
                    return
                selected_sheet = sheet_var.currentText()
                df = pd.read_excel(file_path, sheet_name=selected_sheet)
                print(f"Excel sheet '{selected_sheet}' loaded: {df.shape} rows, columns: {list(df.columns)}")

            dialog = ImportDataDialog(self, df)
            if dialog.exec_() == QDialog.Accepted:
                row_range, x_col, y_col, z_col, index_col = dialog.get_selected_data()

                row_slice = self.parse_range(row_range, df.shape[0], "row")
                if row_slice is None:
                    return

                x_col_idx = int(x_col) - 1
                y_col_idx = int(y_col) - 1
                z_col_idx = int(z_col.split(":")[0].strip()) - 1 if z_col != "None" else None
                index_col_idx = int(index_col.split(":")[0].strip()) - 1 if index_col != "None" else None

                columns_to_extract = [x_col_idx, y_col_idx]
                if z_col_idx is not None:
                    columns_to_extract.append(z_col_idx)
                if index_col_idx is not None:
                    columns_to_extract.append(index_col_idx)

                subset_df = df.iloc[row_slice, columns_to_extract]
                subset_df = subset_df.dropna()

                x_data = pd.to_numeric(subset_df.iloc[:, 0], errors='coerce').values
                y_data = pd.to_numeric(subset_df.iloc[:, 1], errors='coerce').values

                if not np.any(~np.isnan(x_data)) or not np.any(~np.isnan(y_data)):
                    QMessageBox.critical(self, "Error", "Selected X or Y column contains only NaN values.")
                    return

                z_data = None
                if z_col_idx is not None:
                    z_data = subset_df.iloc[:, 2 if index_col_idx is None else 3].values
                    try:
                        z_data_numeric = pd.to_numeric(z_data, errors='coerce')
                        if np.all(np.isnan(z_data_numeric)):
                            z_data = z_data.astype(str)
                        else:
                            z_data = z_data_numeric
                    except Exception:
                        z_data = z_data.astype(str)

                index_data = None
                if index_col_idx is not None:
                    index_data = subset_df.iloc[:, 2 if z_col_idx is None else 3].values

                valid_mask = ~np.isnan(x_data) & ~np.isnan(y_data)
                x_data = x_data[valid_mask]
                y_data = y_data[valid_mask]
                if z_data is not None:
                    z_data = z_data[valid_mask]
                if index_data is not None:
                    index_data = index_data[valid_mask]

                if len(x_data) == 0 or len(y_data) == 0:
                    QMessageBox.critical(self, "Error", "No valid numeric data remaining.")
                    return

                x_col_name = df.columns[x_col_idx]
                y_col_name = df.columns[y_col_idx]
                z_col_name = df.columns[z_col_idx] if z_col_idx is not None else None
                index_col_name = df.columns[index_col_idx] if index_col_idx is not None else None

                self.data.clear()
                self.data[x_col_name] = x_data
                self.data[y_col_name] = y_data
                if z_col_name:
                    self.data[z_col_name] = z_data
                if index_col_name:
                    self.data[index_col_name] = index_data

                for combo in [self.x_data_combo, self.y_data_combo, self.z_data_combo, self.index_data_combo]:
                    combo.clear()
                    combo.addItems([x_col_name, y_col_name] + ([z_col_name] if z_col_name else []) + ([index_col_name] if index_col_name else []))
                self.x_data_combo.setCurrentText(x_col_name)
                self.y_data_combo.setCurrentText(y_col_name)
                self.z_data_combo.setCurrentText(z_col_name if z_col_name else "")
                self.index_data_combo.setCurrentText(index_col_name if index_col_name else "")

                self.perform_fit()
        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to load file: {e}")

    def on_fit_type_change(self):
        fit_type = self.fit_type_combo.currentText()
        self.custom_func_widget.setVisible(fit_type == "Custom")
        self.predefined_func_widget.setVisible(fit_type == "Pre-defined Custom")
        self.degree_widget.setVisible(fit_type == "Polynomial")
        if self.auto_fit_check.isChecked():
            self.perform_fit()

    def on_param_change(self):
        if self.auto_fit_check.isChecked():
            self.perform_fit()

    def apply_plot_settings(self):
        try:
            x_min = float(self.x_min_edit.text())
            x_max = float(self.x_max_edit.text())
            y_min = float(self.y_min_edit.text())
            y_max = float(self.y_max_edit.text())
            point_size = float(self.point_size_edit.text())
            tick_step = float(self.tick_step_edit.text())
            if x_min >= x_max or y_min >= y_max or point_size <= 0 or tick_step <= 0:
                raise ValueError("Invalid settings: Ensure min < max and positive values.")

            background_color = self.background_color_combo.currentText().lower().replace(" ", "")
            self.plot_widget.setBackground(background_color)

            # Define font properties for axis titles
            font_family = self.axis_title_font_type.currentText()
            font_size = self.axis_title_font_size.value()
            x_text = self.x_title_edit.text()
            y_text = self.y_title_edit.text()

            # Build style string with conditional formatting
            title_style = f"font-family:{font_family}; font-size:{font_size}pt;"
            if self.axis_title_bold_check.isChecked():
                title_style += " font-weight:bold;"
            if self.axis_title_italic_check.isChecked():
                title_style += " font-style:italic;"

            # Create HTML-styled text for axis titles
            x_html = f'<span style="{title_style}">{x_text}</span>'
            y_html = f'<span style="{title_style}">{y_text}</span>'

            # Set labels with HTML text
            self.plot_widget.setLabel('bottom', x_html)
            self.plot_widget.setLabel('left', y_html)

            # Set tick fonts with bold and italic options
            axis_tick_font = QFont(self.axis_tick_font_type.currentText())
            axis_tick_font.setPointSize(self.axis_tick_font_size.value())
            axis_tick_font.setBold(self.axis_tick_bold_check.isChecked())
            axis_tick_font.setItalic(self.axis_tick_italic_check.isChecked())

            self.plot_widget.getAxis('bottom').setStyle(tickFont=axis_tick_font)
            self.plot_widget.getAxis('left').setStyle(tickFont=axis_tick_font)

            x_ticks = np.arange(x_min, x_max + tick_step, tick_step)
            y_ticks = np.arange(y_min, y_max + tick_step, tick_step)
            self.plot_widget.getAxis('bottom').setTicks([[(v, f"{v:.2f}") for v in x_ticks]])
            self.plot_widget.getAxis('left').setTicks([[(v, f"{v:.2f}") for v in y_ticks]])

            self.perform_fit()
        except ValueError as e:
            QMessageBox.critical(self, "Error", f"Invalid plot settings: {e}")

    def perform_fit(self):
        try:
            x_key = self.x_data_combo.currentText()
            y_key = self.y_data_combo.currentText()
            z_key = self.z_data_combo.currentText()
            index_key = self.index_data_combo.currentText()

            if not self.validate_data(x_key, y_key):
                return

            x, y, z, index = self.prepare_data(x_key, y_key, z_key, index_key)
            
            x_min = float(self.x_min_edit.text())
            x_max = float(self.x_max_edit.text())
            x_plot = np.linspace(x_min, x_max, 100)
            
            fit_type = self.fit_type_combo.currentText()
            try:
                fitted_y, y_plot, equation, params, y_lower, y_upper = self.fit_model(fit_type, x, y, x_plot)
            except Exception as e:
                QMessageBox.critical(self, "Fitting Error", f"Error during model fitting: {str(e)}")
                return

            self.update_result_labels(equation, params, y, fitted_y)
            self.update_plot(x, y, z, index, x_plot, y_plot, y_lower, y_upper)

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Fitting failed: {str(e)}\n\n{traceback.format_exc()}")

    def validate_data(self, x_key, y_key):
        if x_key not in self.data or y_key not in self.data:
            QMessageBox.critical(self, "Error", "Selected X or Y data not found.")
            return False
        return True

    def prepare_data(self, x_key, y_key, z_key, index_key):
        x = self.data[x_key]
        y = self.data[y_key]
        z = self.data[z_key] if z_key and z_key in self.data else None  # Handle empty z_key
        index = self.data[index_key] if index_key and index_key in self.data else None
        
        min_len = min(len(x), len(y))
        x = x[:min_len]
        y = y[:min_len]
        z = z[:min_len] if z is not None else None
        index = index[:min_len] if index is not None else None
        
        return x, y, z, index

    def fit_model(self, fit_type, x, y, x_plot):
        if fit_type == "Polynomial":
            return self.fit_polynomial(x, y, x_plot)
        elif fit_type == "Custom":
            return self.fit_custom_function(x, y, x_plot)
        elif fit_type == "Pre-defined Custom":
            return self.fit_predefined_custom(x, y, x_plot)
        else:
            return self.fit_standard_function(fit_type, x, y, x_plot)

    def fit_polynomial(self, x, y, x_plot):
        degree = int(self.degree_combo.currentText())
        coeffs = np.polyfit(x, y, degree)
        poly = np.poly1d(coeffs)
        fitted_y = poly(x)
        y_plot = poly(x_plot)
        
        residuals = y - fitted_y
        n = len(x)
        p = degree + 1
        ser = np.sqrt(np.sum(residuals**2) / (n - p)) if n > p else 0
        
        terms = [f"{c:.4f}x^{degree-i}" if degree-i > 1 else f"{c:.4f}x" if degree-i == 1 else f"{c:.4f}" for i, c in enumerate(coeffs)]
        equation = f"Polynomial (degree {degree}): f(x) = " + " + ".join(terms)
        params = [f"p{i+1} = {c:.4f}" for i, c in enumerate(coeffs)]
        
        return fitted_y, y_plot, equation, params, y_plot - 2*ser, y_plot + 2*ser

    def fit_custom_function(self, x, y, x_plot):
        func_str = self.custom_func_edit.text()
        
        try:
            x_sym = symbols('x')
            expr = sympify(func_str)
            free_symbols = expr.free_symbols
            param_names = sorted([str(s) for s in free_symbols if str(s) != 'x'])
            
            if not param_names:
                model_func = lambdify(x_sym, expr, "numpy")
                fitted_y = model_func(x)
                y_plot = model_func(x_plot)
                residuals = y - fitted_y
                ser = np.sqrt(np.sum(residuals**2) / len(x)) if len(x) > 0 else 0
                equation = f"Custom: f(x) = {func_str}"
                params = []
            else:
                param_syms = [symbols(name) for name in param_names]
                model_func = lambdify([x_sym] + param_syms, expr, "numpy")
                
                def fit_func(x_data, *parameters):
                    return model_func(x_data, *parameters)
                
                p0 = [1.0] * len(param_names)
                popt, _ = curve_fit(fit_func, x, y, p0=p0, maxfev=10000)
                
                fitted_y = fit_func(x, *popt)
                y_plot = fit_func(x_plot, *popt)
                residuals = y - fitted_y
                n = len(x)
                p = len(popt)
                ser = np.sqrt(np.sum(residuals**2) / (n - p)) if n > p else 0
                
                equation = f"Custom: f(x) = {func_str}"
                params = [f"{name} = {val:.4f}" for name, val in zip(param_names, popt)]
            
            return fitted_y, y_plot, equation, params, y_plot - 2*ser, y_plot + 2*ser
        
        except Exception as e:
            raise ValueError(f"Invalid custom function: {str(e)}")

    def fit_predefined_custom(self, x, y, x_plot):
        func_name = self.predefined_func_combo.currentText()
        if func_name not in self.custom_functions:
            raise ValueError(f"Predefined function '{func_name}' not found")
        
        func_info = self.custom_functions[func_name]
        model = func_info["model"]
        equation_template = func_info["equation"]
        param_names = func_info["params"]
        
        p0 = [1.0] * len(param_names)
        popt, _ = curve_fit(model, x, y, p0=p0, maxfev=10000)
        
        fitted_y = model(x, *popt)
        y_plot = model(x_plot, *popt)
        
        residuals = y - fitted_y
        n = len(x)
        p = len(popt)
        ser = np.sqrt(np.sum(residuals**2) / (n - p)) if n > p else 0
        
        equation = equation_template
        for name, val in zip(param_names, popt):
            equation = equation.replace(name, f"{val:.4f}")
        
        params = [f"{name} = {val:.4f}" for name, val in zip(param_names, popt)]
        
        return fitted_y, y_plot, equation, params, y_plot - 2*ser, y_plot + 2*ser

    def fit_standard_function(self, fit_type, x, y, x_plot):
        if fit_type == "Linear":
            def model(x, a, b): return a * x + b
            equation_template = "Linear: f(x) = {a} x + {b}"
            param_names = ["a", "b"]
        elif fit_type == "Power":
            def model(x, a, b): return a * np.power(x, b)
            equation_template = "Power: f(x) = {a} x^{b}"
            param_names = ["a", "b"]
        elif fit_type == "Exponential":
            def model(x, a, b): return a * np.exp(b * x)
            equation_template = "Exponential: f(x) = {a} e^({b} x)"
            param_names = ["a", "b"]
        else:
            raise ValueError(f"Unknown fit type: {fit_type}")
        
        with warnings.catch_warnings():
            warnings.simplefilter("ignore")
            popt, _ = curve_fit(model, x, y, maxfev=10000)
        
        fitted_y = model(x, *popt)
        y_plot = model(x_plot, *popt)
        
        residuals = y - fitted_y
        n = len(x)
        p = len(popt)
        ser = np.sqrt(np.sum(residuals**2) / (n - p)) if n > p else 0
        
        equation = equation_template.format(**{name: f"{val:.4f}" for name, val in zip(param_names, popt)})
        params = [f"{name} = {val:.4f}" for name, val in zip(param_names, popt)]
        
        return fitted_y, y_plot, equation, params, y_plot - 2*ser, y_plot + 2*ser

    def update_result_labels(self, equation, params, y, fitted_y):
        self.equation_label.setText(equation)
        for i, param in enumerate(params):
            if i < len(self.param_labels):
                self.param_labels[i].setText(param)
        for i in range(len(params), len(self.param_labels)):
            self.param_labels[i].setText("")
        
        residuals = y - fitted_y
        sse = np.sum(residuals**2)
        sst = np.sum((y - np.mean(y))**2)
        rsquare = 1 - (sse / sst) if sst != 0 else 0
        rmse = np.sqrt(sse / len(y)) if len(y) > 0 else 0
        n = len(y)
        p = len(params) if params else 0
        ser = np.sqrt(sse / (n - p)) if n > p else 0
        
        self.sse_label.setText(f"SSE: {sse:.4f}")
        self.rsquare_label.setText(f"R-square: {rsquare:.4f}")
        self.rmse_label.setText(f"RMSE: {rmse:.4f}")
        self.ser_label.setText(f"SER: {ser:.4f}")

    def update_plot(self, x, y, z, index, x_plot, y_plot, y_lower, y_upper):
        self.plot_widget.clear()
        point_size = float(self.point_size_edit.text())
        
        # Create scatter plot
        if z is not None and len(z) > 0:  # Ensure z data exists and is not empty
            scatter_item = self.create_colored_scatter(x, y, z, index, point_size)
        else:
            scatter_item = pg.ScatterPlotItem(
                x=x, y=y, size=point_size, pen=pg.mkPen(None), 
                brush=pg.mkBrush('b'), hoverable=True,
                tip=lambda x, y, i: f"x: {x:.2f}\ny: {y:.2f}\nindex: {index[int(i)] if index is not None else int(i)}"
            )
        
        self.plot_widget.addItem(scatter_item)
        
        # Add text labels if index data is provided and checkbox is checked
        if index is not None and len(index) > 0 and self.show_labels_check.isChecked():
            for xi, yi, label in zip(x, y, index):
                # Create text item for each point
                text_item = pg.TextItem(
                    text=str(label), 
                    anchor=(0.5, -0.5),  # Position above point
                    color=(0, 0, 0),     # Black text
                    fill=pg.mkBrush(255, 255, 255, 100)  # Semi-transparent white background
                )
                text_item.setPos(xi, yi)
                # Optional: Adjust font size
                font = text_item.textItem.font()
                font.setPointSize(8)  # Adjust as needed
                text_item.setFont(font)
                self.plot_widget.addItem(text_item)
        
        # Plot the fitted curve
        if y_plot is not None:
            fit_curve = pg.PlotDataItem(x_plot, y_plot, pen=pg.mkPen('r', width=2), name='Fit')
            self.plot_widget.addItem(fit_curve)
            
            if self.show_uncertainty_check.isChecked():
                uncertainty_fill = pg.FillBetweenItem(
                    pg.PlotDataItem(x_plot, y_lower, pen=pg.mkPen('r', width=0)),
                    pg.PlotDataItem(x_plot, y_upper, pen=pg.mkPen('r', width=0)),
                    brush=pg.mkBrush((255, 0, 0, 50))
                )
                self.plot_widget.addItem(uncertainty_fill)
        
        # Set plot ranges and grid
        self.plot_widget.setXRange(float(self.x_min_edit.text()), float(self.x_max_edit.text()))
        self.plot_widget.setYRange(float(self.y_min_edit.text()), float(self.y_max_edit.text()))
        self.plot_widget.showGrid(x=True, y=True)
        self.plot_widget.addLegend()

    def create_colored_scatter(self, x, y, z, index, point_size):
        # Convert z to numeric if possible, handling non-numeric cases
        z_numeric = pd.to_numeric(z, errors='coerce')
        scatter = None
        
        # Remove any existing colorbar
        for item in self.plot_widget.items():
            if isinstance(item, pg.ColorBarItem):
                self.plot_widget.removeItem(item)
        
        if np.isnan(z_numeric).all():  # Non-numeric z-data (categorical)
            unique_z = np.unique(z)
            color_map = {val: pg.intColor(i, len(unique_z)) for i, val in enumerate(unique_z)}
            colors = [color_map[val] for val in z]
            
            scatter = pg.ScatterPlotItem(
                x=x, y=y, size=point_size, pen=pg.mkPen(None),
                brush=[pg.mkBrush(c) for c in colors], hoverable=True,
                tip=lambda x, y, i: f"x: {x:.2f}\ny: {y:.2f}\nz: {z[int(i)]}\nindex: {index[int(i)] if index is not None else int(i)}"
            )
        else:  # Numeric z-data
            z_valid = z_numeric[~np.isnan(z_numeric)]  # Remove NaN values for min/max calculation
            if len(z_valid) > 0:
                z_min = np.min(z_valid)
                z_max = np.max(z_valid)
                cmap = pg.colormap.get(self.colormap_combo.currentText())
                
                if z_min == z_max:  # Single value case
                    colors = [cmap.map(0.5, mode='qcolor')] * len(z_numeric)
                else:
                    z_norm = (z_numeric - z_min) / (z_max - z_min)
                    z_norm = np.nan_to_num(z_norm, nan=0.0)  # Handle NaN values
                    colors = cmap.map(z_norm, mode='qcolor')
                
                brushes = [pg.mkBrush(c) for c in colors]
                
                scatter = pg.ScatterPlotItem(
                    x=x, y=y, size=point_size, pen=pg.mkPen(None),
                    brush=brushes, data=z_numeric, hoverable=True,
                    tip=lambda x, y, i: f"x: {x:.2f}\ny: {y:.2f}\nz: {z[int(i)]}\nindex: {index[int(i)] if index is not None else int(i)}"
                )
                
                # Add colorbar
                color_bar = pg.ColorBarItem(
                    values=(z_min, z_max),
                    cmap=cmap,
                    width=25,
                    interactive=False,
                    orientation='right',
                    label=f"{self.z_data_combo.currentText()}"
                )
                color_bar.setImageItem(scatter)
                self.plot_widget.addItem(color_bar)
            else:
                # Fallback for all-NaN case
                scatter = pg.ScatterPlotItem(
                    x=x, y=y, size=point_size, pen=pg.mkPen(None),
                    brush=pg.mkBrush('b'), hoverable=True,
                    tip=lambda x, y, i: f"x: {x:.2f}\ny: {y:.2f}\nindex: {index[int(i)] if index is not None else int(i)}"
                )
        
        return scatter

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = CurveFittingTool()
    window.show()
    sys.exit(app.exec_())