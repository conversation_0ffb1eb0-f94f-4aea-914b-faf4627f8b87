import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import numpy as np
import pandas as pd
from scipy.optimize import curve_fit
from sympy import sympify, symbols, lambdify
from tkwebview2 import WebView2  # Import tkwebview2
import plotly.graph_objects as go
from plotly.offline import plot

class CurveFittingTool(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("Curve Fitting Tool")
        self.geometry("900x700")

        # Initial sample data
        x = np.linspace(0, 50, 100)
        y = 2 * x + 3 + np.random.normal(0, 5, 100)
        z = np.random.choice(['A', 'B', 'C'], 100)
        index = np.arange(100)
        self.data = {'x': x, 'y': y, 'z': z, 'index': index}

        # Define pre-defined custom functions
        self.custom_functions = {
            "Logistic": {
                "model": lambda x, L, k, x0: L / (1 + np.exp(-k * (x - x0))),
                "equation": "f(x) = L / (1 + e^(-k(x - x0)))",
                "params": ["L", "k", "x0"]
            },
            "Gaussian": {
                "model": lambda x, a, b, c: a * np.exp(-((x - b) ** 2) / (2 * c ** 2)),
                "equation": "f(x) = a * e^(-(x - b)^2 / (2c^2))",
                "params": ["a", "b", "c"]
            },
            "Sinusoidal": {
                "model": lambda x, a, b, c: a * np.sin(b * x + c),
                "equation": "f(x) = a * sin(bx + c)",
                "params": ["a", "b", "c"]
            }
        }

        # Initial plot settings
        self.x_min = tk.DoubleVar(value=0)  # Adjusted to match sample data
        self.x_max = tk.DoubleVar(value=50)
        self.y_min = tk.DoubleVar(value=-10)
        self.y_max = tk.DoubleVar(value=120)
        self.point_size = tk.DoubleVar(value=10)  # Smaller default for Plotly
        self.colormap = tk.StringVar(value='Viridis')  # Plotly uses capitalized names
        self.tick_min = tk.DoubleVar(value=0)
        self.tick_max = tk.DoubleVar(value=1)
        self.tick_step = tk.DoubleVar(value=0.2)

        # --- Control Panel (Left) ---
        control_frame = tk.Frame(self)
        control_frame.pack(side=tk.LEFT, padx=10, pady=10, fill=tk.Y)

        # Select Data File Button
        self.select_file_button = tk.Button(control_frame, text="Select Data File", command=self.import_data)
        self.select_file_button.pack(pady=5)

        # Fit name
        tk.Label(control_frame, text="Fit name:").pack()
        self.fit_name_entry = tk.Entry(control_frame)
        self.fit_name_entry.pack()
        self.fit_name_entry.insert(0, "untitled fit 1")

        # Fit type
        tk.Label(control_frame, text="Fit type:").pack()
        self.fit_type_var = tk.StringVar(value="Linear")
        fit_types = ["Linear", "Power", "Exponential", "Polynomial", "Custom", "Pre-defined Custom"]
        self.fit_type_dropdown = ttk.Combobox(control_frame, textvariable=self.fit_type_var, values=fit_types)
        self.fit_type_dropdown.pack()
        self.fit_type_dropdown.bind("<<ComboboxSelected>>", self.on_fit_type_change)

        # Custom function entry (initially hidden)
        self.custom_func_label = tk.Label(control_frame, text="Custom function (e.g., a*x + b):")
        self.custom_func_entry = tk.Entry(control_frame)
        self.custom_func_entry.insert(0, "a*x + b")

        # Pre-defined custom function dropdown (initially hidden)
        self.predefined_func_label = tk.Label(control_frame, text="Pre-defined function:")
        self.predefined_func_var = tk.StringVar(value="Logistic")
        self.predefined_func_dropdown = ttk.Combobox(control_frame, textvariable=self.predefined_func_var, values=list(self.custom_functions.keys()))
        self.predefined_func_dropdown.pack()

        # Degree (for polynomial)
        self.degree_label = tk.Label(control_frame, text="Degree:")
        self.degree_var = tk.StringVar(value="3")
        degrees = [str(i) for i in range(1, 6)]
        self.degree_dropdown = ttk.Combobox(control_frame, textvariable=self.degree_var, values=degrees)
        self.degree_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Auto fit checkbox
        self.auto_fit_var = tk.BooleanVar(value=False)
        self.auto_fit_check = tk.Checkbutton(control_frame, text="Auto fit", variable=self.auto_fit_var)
        self.auto_fit_check.pack()

        # X data
        tk.Label(control_frame, text="X data:").pack()
        self.x_data_var = tk.StringVar(value="x")
        self.x_data_dropdown = ttk.Combobox(control_frame, textvariable=self.x_data_var)
        self.x_data_dropdown['values'] = list(self.data.keys())
        self.x_data_dropdown.pack()
        self.x_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Y data
        tk.Label(control_frame, text="Y data:").pack()
        self.y_data_var = tk.StringVar(value="y")
        self.y_data_dropdown = ttk.Combobox(control_frame, textvariable=self.y_data_var)
        self.y_data_dropdown['values'] = list(self.data.keys())
        self.y_data_dropdown.pack()
        self.y_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Z data (color key)
        tk.Label(control_frame, text="Z data (color key):").pack()
        self.z_data_var = tk.StringVar(value="z")
        self.z_data_dropdown = ttk.Combobox(control_frame, textvariable=self.z_data_var)
        self.z_data_dropdown['values'] = list(self.data.keys())
        self.z_data_dropdown.pack()
        self.z_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Index data
        tk.Label(control_frame, text="Index data:").pack()
        self.index_data_var = tk.StringVar(value="index")
        self.index_data_dropdown = ttk.Combobox(control_frame, textvariable=self.index_data_var)
        self.index_data_dropdown['values'] = list(self.data.keys())
        self.index_data_dropdown.pack()
        self.index_data_dropdown.bind("<<ComboboxSelected>>", self.on_param_change)

        # Fit and Stop buttons
        self.fit_button = tk.Button(control_frame, text="Fit", command=self.perform_fit)
        self.fit_button.pack(pady=5)
        self.stop_button = tk.Button(control_frame, text="Stop", state="disabled")
        self.stop_button.pack()

        # Results frame
        results_frame = tk.LabelFrame(control_frame, text="Results")
        results_frame.pack(pady=10, fill=tk.X)
        self.equation_label = tk.Label(results_frame, text="", wraplength=200)
        self.equation_label.pack()
        self.param_labels = []
        for i in range(5):
            label = tk.Label(results_frame, text="")
            label.pack()
            self.param_labels.append(label)
        self.sse_label = tk.Label(results_frame, text="SSE: ")
        self.sse_label.pack()
        self.rsquare_label = tk.Label(results_frame, text="R-square: ")
        self.rsquare_label.pack()
        self.rmse_label = tk.Label(results_frame, text="RMSE: ")
        self.rmse_label.pack()

        # --- Plot Settings Frame ---
        settings_frame = tk.LabelFrame(control_frame, text="Plot Settings")
        settings_frame.pack(pady=10, fill=tk.X)

        tk.Label(settings_frame, text="X Min:").grid(row=0, column=0, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.x_min).grid(row=0, column=1, padx=5, pady=2)
        tk.Label(settings_frame, text="X Max:").grid(row=0, column=2, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.x_max).grid(row=0, column=3, padx=5, pady=2)

        tk.Label(settings_frame, text="Y Min:").grid(row=1, column=0, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.y_min).grid(row=1, column=1, padx=5, pady=2)
        tk.Label(settings_frame, text="Y Max:").grid(row=1, column=2, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.y_max).grid(row=1, column=3, padx=5, pady=2)

        tk.Label(settings_frame, text="Point Size:").grid(row=2, column=0, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.point_size).grid(row=2, column=1, padx=5, pady=2)

        tk.Label(settings_frame, text="Colormap:").grid(row=3, column=0, padx=5, pady=2)
        colormap_options = ['Viridis', 'Plasma', 'Inferno', 'Magma', 'Cividis']  # Plotly-compatible names
        ttk.Combobox(settings_frame, textvariable=self.colormap, values=colormap_options).grid(row=3, column=1, padx=5, pady=2)

        tk.Label(settings_frame, text="Tick Min:").grid(row=4, column=0, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.tick_min).grid(row=4, column=1, padx=5, pady=2)
        tk.Label(settings_frame, text="Tick Max:").grid(row=4, column=2, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.tick_max).grid(row=4, column=3, padx=5, pady=2)
        tk.Label(settings_frame, text="Tick Step:").grid(row=5, column=0, padx=5, pady=2)
        tk.Entry(settings_frame, textvariable=self.tick_step).grid(row=5, column=1, padx=5, pady=2)

        tk.Button(settings_frame, text="Apply Settings", command=self.apply_plot_settings).grid(row=6, column=0, columnspan=4, pady=5)

        # --- Plot Frame (Right) with WebView2 ---
        plot_frame = tk.Frame(self)
        plot_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        self.webview = WebView2(plot_frame, width=600, height=500)
        self.webview.pack(fill=tk.BOTH, expand=True)

        # Initial setup
        self.on_fit_type_change()
        self.perform_fit()

    def parse_range(self, range_str, max_value, label):
        # [Same as your original implementation]
        if not range_str:
            return slice(None)
        try:
            if '-' in range_str:
                start, end = map(int, range_str.split('-'))
                if start < 1 or end > max_value or start > end:
                    raise ValueError(f"Invalid {label} range. Must be between 1 and {max_value} and start <= end.")
                return slice(start - 1, end)
            else:
                idx = int(range_str) - 1
                if idx < 0 or idx >= max_value:
                    raise ValueError(f"Invalid {label} index. Must be between 1 and {max_value}.")
                return slice(idx, idx + 1)
        except ValueError as e:
            messagebox.showerror("Error", str(e))
            return None

    def find_first_numeric_column(self, df):
        # [Same as your original implementation]
        for i, col in enumerate(df.columns):
            try:
                if pd.to_numeric(df[col], errors='coerce').notna().any():
                    return i
            except Exception:
                continue
        return 0

    def import_data(self):
        # [Same as your original implementation, with perform_fit called at the end]
        # ...
        self.perform_fit()

    def on_fit_type_change(self, *args):
        # [Same as your original implementation]
        fit_type = self.fit_type_var.get()
        if fit_type == "Custom":
            self.custom_func_label.pack()
            self.custom_func_entry.pack()
            self.predefined_func_label.pack_forget()
            self.predefined_func_dropdown.pack_forget()
            self.degree_label.pack_forget()
            self.degree_dropdown.pack_forget()
        elif fit_type == "Polynomial":
            self.custom_func_label.pack_forget()
            self.custom_func_entry.pack_forget()
            self.predefined_func_label.pack_forget()
            self.predefined_func_dropdown.pack_forget()
            self.degree_label.pack()
            self.degree_dropdown.pack()
        elif fit_type == "Pre-defined Custom":
            self.custom_func_label.pack_forget()
            self.custom_func_entry.pack_forget()
            self.predefined_func_label.pack()
            self.predefined_func_dropdown.pack()
            self.degree_label.pack_forget()
            self.degree_dropdown.pack_forget()
        else:
            self.custom_func_label.pack_forget()
            self.custom_func_entry.pack_forget()
            self.predefined_func_label.pack_forget()
            self.predefined_func_dropdown.pack_forget()
            self.degree_label.pack_forget()
            self.degree_dropdown.pack_forget()
        if self.auto_fit_var.get():
            self.perform_fit()

    def on_param_change(self, *args):
        if self.auto_fit_var.get():
            self.perform_fit()

    def apply_plot_settings(self):
        try:
            x_min = float(self.x_min.get())
            x_max = float(self.x_max.get())
            y_min = float(self.y_min.get())
            y_max = float(self.y_max.get())
            point_size = float(self.point_size.get())
            tick_min = float(self.tick_min.get())
            tick_max = float(self.tick_max.get())
            tick_step = float(self.tick_step.get())

            if x_min >= x_max or y_min >= y_max or point_size <= 0 or tick_min >= tick_max or tick_step <= 0:
                raise ValueError("Invalid settings: Ensure min < max and positive values.")
            self.perform_fit()  # Redraw with updated settings
        except ValueError as e:
            messagebox.showerror("Error", f"Invalid plot settings: {e}")

    def perform_fit(self):
        """Perform fitting and render with Plotly via tkwebview2"""
        try:
            x_key = self.x_data_var.get()
            y_key = self.y_data_var.get()
            z_key = self.z_data_var.get()
            index_key = self.index_data_var.get()

            if x_key not in self.data or y_key not in self.data:
                messagebox.showerror("Error", "Selected X or Y data not found.")
                return

            x = self.data[x_key]
            y = self.data[y_key]
            z = self.data[z_key] if z_key in self.data else None
            index = self.data[index_key] if index_key in self.data else None

            min_len = min(len(x), len(y))
            x, y = x[:min_len], y[:min_len]
            z = z[:min_len] if z is not None else None
            index = index[:min_len] if index is not None else None

            fit_type = self.fit_type_var.get()
            if fit_type == "Polynomial":
                degree = int(self.degree_var.get())
                coeffs = np.polyfit(x, y, degree)
                poly = np.poly1d(coeffs)
                fitted_y = poly(x)
                equation = f"Polynomial (degree {degree}): f(x) = " + " + ".join(
                    [f"{c:.2f}x^{degree-i}" if i < degree else f"{c:.2f}" for i, c in enumerate(coeffs)]
                )
                params = [f"p{i+1} = {c:.2f}" for i, c in enumerate(coeffs)]
                x_plot = np.linspace(float(self.x_min.get()), float(self.x_max.get()), 100)
                y_plot = poly(x_plot)
            elif fit_type == "Custom":
                func_str = self.custom_func_entry.get()
                params = symbols('a b c d e')
                x_sym = symbols('x')
                expr = sympify(func_str)
                model = lambdify((x_sym, *params), expr, "numpy")
                param_names = [str(p) for p in params if p in expr.free_symbols]
                popt, _ = curve_fit(model, x, y, p0=[1.0] * len(param_names), maxfev=10000)
                fitted_y = model(x, *popt)
                equation = f"Custom: f(x) = {func_str}"
                params = [f"{name} = {val:.2f}" for name, val in zip(param_names, popt)]
                x_plot = np.linspace(float(self.x_min.get()), float(self.x_max.get()), 100)
                y_plot = model(x_plot, *popt)
            elif fit_type == "Pre-defined Custom":
                func_name = self.predefined_func_var.get()
                model = self.custom_functions[func_name]["model"]
                equation = self.custom_functions[func_name]["equation"]
                param_names = self.custom_functions[func_name]["params"]
                popt, _ = curve_fit(model, x, y, p0=[1.0] * len(param_names), maxfev=10000)
                fitted_y = model(x, *popt)
                params = [f"{name} = {val:.2f}" for name, val in zip(param_names, popt)]
                x_plot = np.linspace(float(self.x_min.get()), float(self.x_max.get()), 100)
                y_plot = model(x_plot, *popt)
            else:
                if fit_type == "Linear":
                    def model(x, a, b): return a * x + b
                    equation_template = "Linear: f(x) = a x + b"
                    param_names = ["a", "b"]
                elif fit_type == "Power":
                    def model(x, a, b): return a * x**b
                    equation_template = "Power: f(x) = a x^b"
                    param_names = ["a", "b"]
                elif fit_type == "Exponential":
                    def model(x, a, b): return a * np.exp(b * x)
                    equation_template = "Exponential: f(x) = a e^(b x)"
                    param_names = ["a", "b"]
                popt, _ = curve_fit(model, x, y, maxfev=10000)
                fitted_y = model(x, *popt)
                equation = equation_template.replace("a", f"{popt[0]:.2f}").replace("b", f"{popt[1]:.2f}")
                params = [f"{name} = {val:.2f}" for name, val in zip(param_names, popt)]
                x_plot = np.linspace(float(self.x_min.get()), float(self.x_max.get()), 100)
                y_plot = model(x_plot, *popt)

            # Update results
            self.equation_label.config(text=equation)
            for i, param in enumerate(params):
                self.param_labels[i].config(text=param)
            for i in range(len(params), 5):
                self.param_labels[i].config(text="")
            residuals = y - fitted_y
            sse = np.sum(residuals**2)
            sst = np.sum((y - np.mean(y))**2)
            rsquare = 1 - (sse / sst) if sst != 0 else 0
            rmse = np.sqrt(sse / len(y))
            self.sse_label.config(text=f"SSE: {sse:.2f}")
            self.rsquare_label.config(text=f"R-square: {rsquare:.4f}")
            self.rmse_label.config(text=f"RMSE: {rmse:.2f}")

            # Create Plotly figure
            fig = go.Figure()

            # Scatter plot with Z-based coloring
            if z is not None:
                z_numeric = pd.to_numeric(z, errors='coerce')
                if z_numeric.isna().all():
                    # Categorical Z
                    scatter = go.Scatter(
                        x=x, y=y, mode='markers',
                        marker=dict(size=self.point_size.get(), color=z, colorscale=self.colormap.get(), showscale=True),
                        text=index, hoverinfo='x+y+text', name='Data'
                    )
                else:
                    # Numeric Z
                    scatter = go.Scatter(
                        x=x, y=y, mode='markers',
                        marker=dict(size=self.point_size.get(), color=z_numeric, colorscale=self.colormap.get(), showscale=True),
                        text=index, hoverinfo='x+y+text', name='Data'
                    )
                    if self.tick_step.get() > 0:
                        fig.update_coloraxes(colorbar_tickmode='array',
                                             colorbar_tickvals=np.arange(self.tick_min.get(), self.tick_max.get() + self.tick_step.get()/2, self.tick_step.get()))
            else:
                scatter = go.Scatter(
                    x=x, y=y, mode='markers',
                    marker=dict(size=self.point_size.get(), color='black'),
                    text=index, hoverinfo='x+y+text', name='Data'
                )
            fig.add_trace(scatter)

            # Fitted curve
            fig.add_trace(go.Scatter(x=x_plot, y=y_plot, mode='lines', name='Fit', line=dict(color='blue')))

            # Update layout
            fig.update_layout(
                xaxis_range=[self.x_min.get(), self.x_max.get()],
                yaxis_range=[self.y_min.get(), self.y_max.get()],
                showlegend=True, template='plotly_white',
                width=600, height=500  # Match WebView2 size
            )

            # Render in tkwebview2
            html_str = plot(fig, output_type='div', include_plotlyjs='cdn')
            self.webview.load_html(html_str)

        except Exception as e:
            messagebox.showerror("Error", f"Fitting failed: {e}")

if __name__ == "__main__":
    app = CurveFittingTool()
    app.mainloop()